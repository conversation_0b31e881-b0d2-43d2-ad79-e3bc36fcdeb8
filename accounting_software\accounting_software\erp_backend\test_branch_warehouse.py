#!/usr/bin/env python
"""
Test to confirm branch warehouse is used by default for inventory checking
"""
import os
import django
from decimal import Decimal
from datetime import date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from inventory.models import Warehouse, Inventory
from Pricing.models import Product
from sales.models import Invoice, InvoiceLineItem, PaymentTerm
from contacts.models import Contact

def test_branch_warehouse_default():
    print('🧪 Testing Branch Warehouse Default Selection')
    print('=' * 50)
    
    # Get warehouses
    main_warehouse = Warehouse.objects.get(warehouse_type='MAIN')
    branch_warehouse = Warehouse.objects.get(warehouse_type='BRANCH')
    
    print(f'📋 Main Warehouse: {main_warehouse.name} (ID: {main_warehouse.warehouse_id})')
    print(f'🏢 Branch Warehouse: {branch_warehouse.name} (ID: {branch_warehouse.warehouse_id})')
    print()
    
    # Check current inventory in both warehouses
    laptop = Product.objects.get(code='LAPTOP001')
    
    main_inventory = Inventory.objects.filter(product=laptop, warehouse=main_warehouse).first()
    branch_inventory = Inventory.objects.filter(product=laptop, warehouse=branch_warehouse).first()
    
    print(f'📦 Current Inventory for {laptop.code}:')
    if main_inventory:
        print(f'   Main Warehouse: {main_inventory.quantity_on_hand} units')
    else:
        print(f'   Main Warehouse: No inventory record')
    
    if branch_inventory:
        print(f'   Branch Warehouse: {branch_inventory.quantity_on_hand} units')
    else:
        print(f'   Branch Warehouse: No inventory record')
    print()
    
    # Create test invoice and line item
    customer = Contact.objects.filter(contact_type='customer').first()
    payment_term = PaymentTerm.objects.filter(is_active=True).first()
    
    invoice = Invoice.objects.create(
        customer=customer,
        invoice_date=date.today(),
        status='draft',
        payment_terms=payment_term
    )
    
    line_item = InvoiceLineItem.objects.create(
        invoice=invoice,
        product=laptop,
        quantity=Decimal('5.00')
    )
    
    print(f'✅ Created test invoice: {invoice.invoice_number}')
    print(f'✅ Added line item: {laptop.code} x {line_item.quantity}')
    print()
    
    # Test inventory checking WITHOUT specifying warehouse_id
    print('🔍 Testing inventory check without specifying warehouse...')
    is_available, available_qty, shortage, warehouse_info = line_item.check_inventory_availability()
    
    print(f'📊 Results:')
    print(f'   Default warehouse used: {warehouse_info["warehouse_name"]} (ID: {warehouse_info["warehouse_id"]})')
    print(f'   Available quantity: {available_qty}')
    print(f'   Required quantity: {warehouse_info["required_quantity"]}')
    print(f'   Is available: {"✅ YES" if is_available else "❌ NO"}')
    
    if shortage > 0:
        print(f'   Shortage: {shortage}')
    print()
    
    # Verify it's using branch warehouse
    if warehouse_info["warehouse_id"] == branch_warehouse.warehouse_id:
        print('✅ SUCCESS: System correctly defaults to Branch Warehouse!')
    else:
        print('❌ ISSUE: System is not using Branch Warehouse by default')
        print(f'   Expected: Branch Warehouse (ID: {branch_warehouse.warehouse_id})')
        print(f'   Actual: {warehouse_info["warehouse_name"]} (ID: {warehouse_info["warehouse_id"]})')
    
    print()
    print('🎯 Test Complete!')
    
    return warehouse_info["warehouse_id"] == branch_warehouse.warehouse_id

if __name__ == '__main__':
    test_branch_warehouse_default()

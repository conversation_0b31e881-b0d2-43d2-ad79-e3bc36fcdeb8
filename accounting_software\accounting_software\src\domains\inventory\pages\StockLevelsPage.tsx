/**
 * Stock Levels Page
 * Enterprise inventory stock level management using Material-UI
 */

import React, { useState, useEffect } from 'react';
import {
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  CircularProgress,
  Alert,
  LinearProgress,
  Tooltip,
  IconButton,
  TablePagination,
} from '@mui/material';
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  FileDownload as ExportIcon,
  Warning as WarningIcon,
  Stop as StopIcon,
  CheckCircle as CheckIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { inventoryService, StockLevel, Warehouse } from '../services/inventory.service';

const StockLevelsPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  const [loading, setLoading] = useState(true);
  const [stockLevels, setStockLevels] = useState<StockLevel[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [error, setError] = useState<string | null>(null);

  // Filters
  const [searchText, setSearchText] = useState('');
  const [selectedWarehouse, setSelectedWarehouse] = useState<number | ''>('');
  const [stockFilter, setStockFilter] = useState<string>('all');

  useEffect(() => {
    loadWarehouses();
  }, []);

  useEffect(() => {
    // Load filters from URL params
    const filter = searchParams.get('filter');
    const warehouse = searchParams.get('warehouse');
    const search = searchParams.get('search');

    if (filter) setStockFilter(filter);
    if (warehouse) setSelectedWarehouse(parseInt(warehouse));
    if (search) setSearchText(search);

    loadStockLevels();
  }, [page, rowsPerPage, searchText, selectedWarehouse, stockFilter, searchParams]);

  const loadWarehouses = async () => {
    try {
      const data = await inventoryService.getWarehouses();
      setWarehouses(data);
    } catch (err) {
      console.error('❌ Error loading warehouses:', err);
    }
  };

  const loadStockLevels = async () => {
    try {
      setLoading(true);
      setError(null);

      const params: any = {
        page: page + 1, // API uses 1-based pagination
        page_size: rowsPerPage,
      };

      if (searchText) params.search = searchText;
      if (selectedWarehouse) params.warehouse = selectedWarehouse;
      if (stockFilter === 'low_stock') params.low_stock = true;
      if (stockFilter === 'out_of_stock') params.out_of_stock = true;

      const response = await inventoryService.getStockLevels(params);
      setStockLevels(response.results);
      setTotal(response.count);

    } catch (err) {
      console.error('❌ Error loading stock levels:', err);
      setError('Failed to load stock levels. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
    setPage(0);
    updateUrlParams({ search: value || undefined });
  };

  const handleWarehouseChange = (value: number | '') => {
    setSelectedWarehouse(value);
    setPage(0);
    updateUrlParams({ warehouse: value ? value.toString() : undefined });
  };

  const handleStockFilterChange = (value: string) => {
    setStockFilter(value);
    setPage(0);
    updateUrlParams({ filter: value === 'all' ? undefined : value });
  };

  const updateUrlParams = (updates: Record<string, string | undefined>) => {
    const newParams = new URLSearchParams(searchParams);

    Object.entries(updates).forEach(([key, value]) => {
      if (value) {
        newParams.set(key, value);
      } else {
        newParams.delete(key);
      }
    });

    setSearchParams(newParams);
  };

  const formatCurrency = (value: string | number) => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(num);
  };

  const formatNumber = (value: string | number) => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-US', { maximumFractionDigits: 4 }).format(num);
  };

  const getStockStatus = (record: StockLevel) => {
    if (record.is_out_of_stock) {
      return <Chip icon={<StopIcon />} label="Out of Stock" color="error" size="small" />;
    } else if (record.is_below_reorder_point) {
      return <Chip icon={<WarningIcon />} label="Low Stock" color="warning" size="small" />;
    } else {
      return <Chip icon={<CheckIcon />} label="In Stock" color="success" size="small" />;
    }
  };

  const getStockProgress = (record: StockLevel) => {
    const onHand = parseFloat(record.quantity_on_hand);
    const reorderPoint = parseFloat(record.reorder_point);
    const maxLevel = parseFloat(record.max_stock_level);

    if (maxLevel > 0) {
      const percentage = (onHand / maxLevel) * 100;
      let color: 'success' | 'warning' | 'error' = 'success';

      if (onHand <= 0) color = 'error';
      else if (onHand <= reorderPoint) color = 'warning';

      return (
        <Box sx={{ width: '100%' }}>
          <LinearProgress
            variant="determinate"
            value={Math.min(percentage, 100)}
            color={color}
            sx={{ height: 8, borderRadius: 4 }}
          />
        </Box>
      );
    }
    return null;
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Stock Levels
      </Typography>
      <Typography variant="body1" color="text.secondary">
        Monitor inventory levels across all warehouses
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}

      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <CircularProgress />
        </Box>
      )}
    </Box>
  );
};

export default StockLevelsPage;
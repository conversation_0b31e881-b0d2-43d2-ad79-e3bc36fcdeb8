from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone

class Product(models.Model):
    """Core product model that can be either goods or services"""
    PRODUCT = 'product'
    SERVICE = 'service'
    TYPE_CHOICES = [
        (PRODUCT, 'Product'),
        (SERVICE, 'Service'),
    ]
    
    # Costing Methods
    STANDARD = 'standard'
    AVERAGE = 'average'
    FIFO = 'fifo'
    COST_METHOD_CHOICES = [
        (STANDARD, 'Standard Cost'),
        (AVERAGE, 'Average Cost'),
        (FIFO, 'FIFO'),
    ]
    
    code = models.CharField(max_length=50, unique=True, help_text="Unique product identifier/SKU")
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    type = models.CharField(max_length=10, choices=TYPE_CHOICES, default=PRODUCT)
    category = models.ForeignKey('sales.ProductCategory', on_delete=models.SET_NULL, null=True, blank=True)
    uom = models.CharField(max_length=20, default='each', help_text="Unit of Measure (each, kg, liter, etc.)")
    
    # Cost fields
    cost_method = models.CharField(max_length=10, choices=COST_METHOD_CHOICES, default=STANDARD)
    standard_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, 
                                      validators=[MinValueValidator(0)])
    average_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, 
                                     validators=[MinValueValidator(0)], editable=False)
    last_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, 
                                  validators=[MinValueValidator(0)], editable=False)
    
    # Tax
    tax_code = models.ForeignKey('sales_tax.SalesTax', on_delete=models.SET_NULL, null=True, blank=True)
    
    # Tracking
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.code} - {self.name}"
    
    class Meta:
        ordering = ['code']
        verbose_name = "Product"
        verbose_name_plural = "Products"

class PriceList(models.Model):
    """Master price list that can be assigned to customers"""

    # Currency choices based on the system's supported currencies
    CURRENCY_CHOICES = [
        ('USD', 'US Dollar ($)'),
        ('EUR', 'Euro (€)'),
        ('GBP', 'British Pound (£)'),
        ('JPY', 'Japanese Yen (¥)'),
        ('CAD', 'Canadian Dollar (C$)'),
        ('AUD', 'Australian Dollar (A$)'),
        ('INR', 'Indian Rupee (₹)'),
        ('CNY', 'Chinese Yuan (¥)'),
        ('NGN', 'Nigerian Naira (₦)'),
        ('ZAR', 'South African Rand (R)'),
    ]

    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES, default='USD')
    valid_from = models.DateField(default=timezone.now)
    valid_to = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    is_default = models.BooleanField(default=False,
                                   help_text="Use as default price list when no specific list is assigned")
    
    def __str__(self):
        return self.name
    
    class Meta:
        ordering = ['name']
        verbose_name = "Price List"
        verbose_name_plural = "Price Lists"

class PriceListItem(models.Model):
    """Individual items within a price list"""
    price_list = models.ForeignKey(PriceList, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey('Pricing.Product', on_delete=models.CASCADE)
    unit_price = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(0)])
    min_quantity = models.DecimalField(max_digits=10, decimal_places=2, default=1,
                                     validators=[MinValueValidator(0.01)],
                                     help_text="Minimum quantity for this price to apply")
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0,
                                         validators=[MinValueValidator(0), MaxValueValidator(100)],
                                         help_text="Additional discount percentage")
    effective_date = models.DateField(default=timezone.now)
    expiry_date = models.DateField(null=True, blank=True)
    
    def __str__(self):
        return f"{self.price_list.name} - {self.product.code} @ {self.unit_price}"
    
    class Meta:
        ordering = ['price_list', 'product']
        unique_together = ('price_list', 'product', 'min_quantity')
        verbose_name = "Price List Item"
        verbose_name_plural = "Price List Items"

class CustomerPriceList(models.Model):
    """Mapping customers to specific price lists"""
    customer = models.ForeignKey('contacts.Customer', on_delete=models.CASCADE)
    price_list = models.ForeignKey(PriceList, on_delete=models.CASCADE)
    effective_date = models.DateField(default=timezone.now)
    expiry_date = models.DateField(null=True, blank=True)
    
    class Meta:
        unique_together = ('customer', 'price_list')
        verbose_name = "Customer Price List"
        verbose_name_plural = "Customer Price Lists"

class DiscountRule(models.Model):
    """Rules for applying discounts based on various criteria"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2,
                                         validators=[MinValueValidator(0), MaxValueValidator(100)])
    # Criteria fields
    min_quantity = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    # Note: CustomerGroup model doesn't exist yet, so using CharField for now
    customer_group = models.CharField(max_length=100, null=True, blank=True,
                                    help_text="Customer group name for discount eligibility")
    product_category = models.ForeignKey('sales.ProductCategory', on_delete=models.CASCADE, null=True, blank=True)
    start_date = models.DateField(default=timezone.now)
    end_date = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    
    def __str__(self):
        return self.name
    
    class Meta:
        ordering = ['name']
        verbose_name = "Discount Rule"
        verbose_name_plural = "Discount Rules"
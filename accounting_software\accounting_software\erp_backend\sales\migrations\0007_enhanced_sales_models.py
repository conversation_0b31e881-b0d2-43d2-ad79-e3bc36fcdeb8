# Generated manually for enhanced sales models

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('sales', '0006_remove_product_expense_account_and_more'),
        ('contacts', '0001_initial'),
    ]

    operations = [
        # Add new fields to Product model for sales price authority
        migrations.AddField(
            model_name='product',
            name='minimum_selling_price',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Minimum allowed selling price (for validation)', max_digits=15),
        ),
        migrations.AddField(
            model_name='product',
            name='price_effective_date',
            field=models.DateField(blank=True, help_text='Date when current price became effective', null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='price_last_updated_at',
            field=models.DateTimeField(blank=True, help_text='When the price was last updated', null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='price_last_updated_by',
            field=models.ForeignKey(blank=True, help_text='User who last updated the sales price', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products_priced', to=settings.AUTH_USER_MODEL),
        ),
        
        # Modify existing unit_price field to add help text
        migrations.AlterField(
            model_name='product',
            name='unit_price',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Sales price (managed by Sales Department)', max_digits=15),
        ),
        migrations.AlterField(
            model_name='product',
            name='cost_price',
            field=models.DecimalField(blank=True, decimal_places=2, default=0.0, help_text='Purchase cost (managed by Purchase Department)', max_digits=15, null=True),
        ),
        
        # Create SalesOrder model
        migrations.CreateModel(
            name='SalesOrder',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('so_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('so_number', models.CharField(max_length=50, unique=True)),
                ('so_date', models.DateField()),
                ('expected_delivery_date', models.DateField(blank=True, null=True)),
                ('sales_rep_name', models.CharField(blank=True, help_text='Name of the sales representative', max_length=200, null=True)),
                ('sales_rep_email', models.EmailField(blank=True, help_text='Email of the sales representative', max_length=254, null=True)),
                ('sales_rep_phone', models.CharField(blank=True, help_text='Phone of the sales representative', max_length=20, null=True)),
                ('subtotal', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('discount_percent', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('amount_invoiced', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('balance_due', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('currency', models.CharField(default='INR', max_length=10)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending'), ('confirmed', 'Confirmed'), ('partial', 'Partially Delivered'), ('delivered', 'Delivered'), ('invoiced', 'Invoiced'), ('closed', 'Closed'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('payment_terms', models.CharField(blank=True, max_length=20, null=True)),
                ('customer_po_number', models.CharField(blank=True, max_length=100, null=True)),
                ('memo', models.TextField(blank=True, help_text='Internal memo', null=True)),
                ('notes', models.TextField(blank=True, help_text='Notes to customer', null=True)),
                ('ship_to_address', models.TextField(blank=True, null=True)),
                ('shipping_method', models.CharField(blank=True, max_length=100, null=True)),
                ('email_sent', models.BooleanField(default=False)),
                ('email_sent_date', models.DateTimeField(blank=True, null=True)),
                ('acknowledged_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sales_orders_created', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, null=True, blank=True, related_name='sales_orders', to='contacts.contact', help_text='Customer from contacts system')),
            ],
            options={
                'db_table': 'sales_orders',
                'ordering': ['-so_date', '-created_at'],
            },
        ),
        
        # Create SalesOrderLineItem model
        migrations.CreateModel(
            name='SalesOrderLineItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(help_text='Auto-filled from product, can be overridden')),
                ('quantity', models.DecimalField(decimal_places=2, default=1.0, max_digits=10)),
                ('unit_of_measure', models.CharField(default='pcs', help_text='Unit of measure (kg, L, pcs, etc.)', max_length=20)),
                ('unit_price', models.DecimalField(decimal_places=2, default=0.0, help_text='Sales price per unit', max_digits=15)),
                ('discount_percent', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('line_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('quantity_delivered', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('quantity_pending', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('taxable', models.BooleanField(default=True)),
                ('tax_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('notes', models.TextField(blank=True, help_text='Line item specific notes', null=True)),
                ('line_order', models.PositiveIntegerField(default=0)),
                ('product', models.ForeignKey(blank=True, help_text='Link to product master for GL integration', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sales_line_items', to='sales.product')),
                ('sales_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='sales.salesorder')),
            ],
            options={
                'db_table': 'sales_order_line_items',
                'ordering': ['line_order'],
            },
        ),
        
        # Create DeliveryNote model
        migrations.CreateModel(
            name='DeliveryNote',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dn_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('dn_number', models.CharField(max_length=50, unique=True)),
                ('delivery_date', models.DateField()),
                ('expected_delivery_date', models.DateField(blank=True, null=True)),
                ('delivered_by', models.CharField(blank=True, help_text='Name of delivery person', max_length=200, null=True)),
                ('delivery_method', models.CharField(blank=True, help_text='Delivery method (truck, courier, etc.)', max_length=100, null=True)),
                ('tracking_number', models.CharField(blank=True, max_length=100, null=True)),
                ('subtotal', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('currency', models.CharField(default='INR', max_length=10)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending'), ('delivered', 'Delivered'), ('invoiced', 'Invoiced'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('posted', models.BooleanField(default=False, help_text='Whether delivery has been posted to inventory')),
                ('posted_date', models.DateTimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, help_text='Delivery notes', null=True)),
                ('customer_signature', models.TextField(blank=True, help_text='Customer signature or acknowledgment', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='delivery_notes_created', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, null=True, blank=True, related_name='delivery_notes', to='contacts.contact', help_text='Customer from contacts system')),
                ('posted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='delivery_notes_posted', to=settings.AUTH_USER_MODEL)),
                ('sales_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='delivery_notes', to='sales.salesorder')),
                ('warehouse', models.ForeignKey(help_text='Warehouse from which goods are delivered', on_delete=django.db.models.deletion.CASCADE, related_name='delivery_notes', to='inventory.warehouse')),
            ],
            options={
                'db_table': 'sales_delivery_notes',
                'ordering': ['-delivery_date', '-created_at'],
            },
        ),
        
        # Create DeliveryNoteItem model
        migrations.CreateModel(
            name='DeliveryNoteItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(help_text='Product description')),
                ('quantity_ordered', models.DecimalField(decimal_places=2, help_text='Original quantity ordered', max_digits=10)),
                ('quantity_delivered', models.DecimalField(decimal_places=2, help_text='Quantity actually delivered', max_digits=10)),
                ('unit_of_measure', models.CharField(default='pcs', max_length=20)),
                ('unit_price', models.DecimalField(decimal_places=2, help_text='Unit price from sales order', max_digits=15)),
                ('line_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('taxable', models.BooleanField(default=True)),
                ('tax_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('batch_number', models.CharField(blank=True, max_length=100, null=True)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, help_text='Item-specific delivery notes', null=True)),
                ('line_order', models.PositiveIntegerField(default=0)),
                ('delivery_note', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='sales.deliverynote')),
                ('product', models.ForeignKey(help_text='Product being delivered', on_delete=django.db.models.deletion.CASCADE, related_name='delivery_items', to='sales.product')),
                ('sales_order_line_item', models.ForeignKey(help_text='Link to original sales order line item', on_delete=django.db.models.deletion.CASCADE, related_name='delivery_items', to='sales.salesorderlineitem')),
            ],
            options={
                'db_table': 'sales_delivery_note_items',
                'ordering': ['line_order'],
            },
        ),
    ] 
"""
Inventory Management Models for ERP System

This module contains all inventory-related models following the ERD:
- Units of Measure (UOM)
- Warehouses
- Inventory (stock levels per warehouse)
- Stock Transactions (all stock movements)
- Goods Receipt Notes (GRN)
- GRN Items
"""

from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid
from datetime import date
from django.utils import timezone


class UnitsOfMeasure(models.Model):
    """
    Units of Measure for products
    Following ERD: units_of_measure table
    """
    uom_id = models.AutoField(primary_key=True)
    unit_name = models.CharField(max_length=100, unique=True, help_text="Full name of the unit")
    abbreviation = models.CharField(max_length=10, unique=True, help_text="Short abbreviation")
    description = models.TextField(blank=True, help_text="Description of the unit")
    
    # Unit type categorization
    unit_type = models.CharField(
        max_length=20,
        choices=[
            ('WEIGHT', 'Weight'),
            ('VOLUME', 'Volume'),
            ('LENGTH', 'Length'),
            ('AREA', 'Area'),
            ('COUNT', 'Count/Pieces'),
            ('TIME', 'Time'),
            ('OTHER', 'Other'),
        ],
        default='COUNT'
    )
    
    # Base unit conversion
    base_unit = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Base unit for conversion (e.g., grams for weight)"
    )
    conversion_factor = models.DecimalField(
        max_digits=15,
        decimal_places=6,
        default=Decimal('1.000000'),
        help_text="Factor to convert to base unit"
    )
    
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'units_of_measure'
        verbose_name = 'Unit of Measure'
        verbose_name_plural = 'Units of Measure'
        ordering = ['unit_name']
    
    def __str__(self):
        return f"{self.unit_name} ({self.abbreviation})"


class Warehouse(models.Model):
    """
    Warehouses for inventory management
    Following ERD: warehouses table
    """
    warehouse_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200, unique=True)
    location = models.CharField(max_length=500, help_text="Physical address/location")
    description = models.TextField(blank=True)
    
    # Warehouse details
    warehouse_code = models.CharField(max_length=20, unique=True, help_text="Short code for warehouse")
    warehouse_type = models.CharField(
        max_length=20,
        choices=[
            ('MAIN', 'Main Warehouse'),
            ('BRANCH', 'Branch Warehouse'),
            ('RETAIL', 'Retail Store'),
            ('TRANSIT', 'Transit Warehouse'),
            ('VIRTUAL', 'Virtual Warehouse'),
        ],
        default='MAIN'
    )
    
    # Contact information
    manager_name = models.CharField(max_length=200, blank=True)
    phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    
    # Settings
    is_active = models.BooleanField(default=True)
    allow_negative_stock = models.BooleanField(default=False)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name='warehouses_created')
    
    class Meta:
        db_table = 'warehouses'
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.warehouse_code})"


class Inventory(models.Model):
    """
    Inventory levels per product per warehouse
    Following ERD: inventory table
    """
    inventory_id = models.AutoField(primary_key=True)
    product = models.ForeignKey('Pricing.Product', on_delete=models.CASCADE, related_name='inventory_levels')
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, related_name='inventory_items')
    
    # Stock quantities
    quantity_on_hand = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        default=Decimal('0.0000'),
        help_text="Current stock quantity"
    )
    quantity_reserved = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        default=Decimal('0.0000'),
        help_text="Quantity reserved for sales orders"
    )
    quantity_on_order = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        default=Decimal('0.0000'),
        help_text="Quantity on purchase orders"
    )
    
    # Reorder settings
    reorder_point = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        default=Decimal('0.0000'),
        help_text="Minimum stock level before reorder"
    )
    reorder_quantity = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        default=Decimal('0.0000'),
        help_text="Standard reorder quantity"
    )
    
    # Cost tracking
    average_cost = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        default=Decimal('0.0000'),
        help_text="Weighted average cost per unit"
    )
    last_cost = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        default=Decimal('0.0000'),
        help_text="Last purchase cost per unit"
    )
    
    # Timestamps
    last_updated = models.DateTimeField(auto_now=True)
    last_transaction_date = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'inventory'
        unique_together = ['product', 'warehouse']
        ordering = ['product__name', 'warehouse__name']
    
    def __str__(self):
        return f"{self.product.name} @ {self.warehouse.name}: {self.quantity_on_hand}"
    
    @property
    def available_quantity(self):
        """Available quantity = On Hand - Reserved"""
        return self.quantity_on_hand - self.quantity_reserved

    @classmethod
    def get_available_stock(cls, product_id, warehouse_id):
        """
        Database-level function to get available stock for a product in a warehouse
        Returns available quantity (on_hand - reserved) or 0 if no inventory record
        """
        try:
            inventory = cls.objects.get(product_id=product_id, warehouse_id=warehouse_id)
            return inventory.available_quantity
        except cls.DoesNotExist:
            return Decimal('0.0000')

    @classmethod
    def check_stock_availability(cls, product_id, warehouse_id, required_quantity):
        """
        Database-level function to check if sufficient stock is available
        Returns tuple: (is_available: bool, available_qty: Decimal, shortage: Decimal)
        """
        available_qty = cls.get_available_stock(product_id, warehouse_id)
        required_qty = Decimal(str(required_quantity))

        is_available = available_qty >= required_qty
        shortage = max(Decimal('0.0000'), required_qty - available_qty)

        return is_available, available_qty, shortage

    @classmethod
    def reserve_stock(cls, product_id, warehouse_id, quantity):
        """
        Database-level function to reserve stock for sales orders/invoices
        Returns True if successful, False if insufficient stock
        """
        try:
            inventory = cls.objects.get(product_id=product_id, warehouse_id=warehouse_id)
            required_qty = Decimal(str(quantity))

            if inventory.available_quantity >= required_qty:
                inventory.quantity_reserved += required_qty
                inventory.save()
                return True
            return False
        except cls.DoesNotExist:
            return False

    @classmethod
    def issue_stock(cls, product_id, warehouse_id, quantity, unit_cost=None):
        """
        Database-level function to issue stock (reduce on_hand quantity)
        Used when invoice is finalized/posted
        Returns True if successful, False if insufficient stock
        """
        try:
            inventory = cls.objects.get(product_id=product_id, warehouse_id=warehouse_id)
            issue_qty = Decimal(str(quantity))

            if inventory.quantity_on_hand >= issue_qty:
                inventory.quantity_on_hand -= issue_qty
                # Also reduce reserved if it was reserved
                if inventory.quantity_reserved >= issue_qty:
                    inventory.quantity_reserved -= issue_qty

                # Update last cost if provided
                if unit_cost:
                    inventory.last_cost = Decimal(str(unit_cost))

                inventory.last_transaction_date = timezone.now()
                inventory.save()
                return True
            return False
        except cls.DoesNotExist:
            return False


class StockTransaction(models.Model):
    """
    All stock movements and transactions
    Following ERD: stock_transactions table
    """
    stock_txn_id = models.AutoField(primary_key=True)
    product = models.ForeignKey('Pricing.Product', on_delete=models.CASCADE, related_name='stock_transactions')
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, related_name='stock_transactions')
    
    # Transaction details
    transaction_type = models.CharField(
        max_length=20,
        choices=[
            ('RECEIPT', 'Goods Receipt'),
            ('ISSUE', 'Stock Issue'),
            ('TRANSFER', 'Warehouse Transfer'),
            ('ADJUSTMENT', 'Stock Adjustment'),
            ('SALE', 'Sale Transaction'),
            ('RETURN', 'Return Transaction'),
            ('DAMAGE', 'Damage/Loss'),
            ('OPENING', 'Opening Balance'),
        ],
        help_text="Type of stock transaction"
    )
    
    # Reference to source document
    reference_type = models.CharField(
        max_length=20,
        choices=[
            ('GRN', 'Goods Receipt Note'),
            ('SO', 'Sales Order'),
            ('PO', 'Purchase Order'),
            ('ADJ', 'Stock Adjustment'),
            ('TRF', 'Transfer'),
            ('RET', 'Return'),
            ('OPEN', 'Opening Balance'),
        ],
        help_text="Type of source document"
    )
    reference_id = models.PositiveIntegerField(help_text="ID of the source document")
    
    # Quantity and cost
    quantity = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        help_text="Quantity moved (positive for in, negative for out)"
    )
    unit_cost = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        default=Decimal('0.0000'),
        help_text="Cost per unit for this transaction"
    )
    total_cost = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Total cost of this transaction"
    )
    
    # Transaction metadata
    txn_date = models.DateTimeField(help_text="Date and time of transaction")
    description = models.TextField(blank=True, help_text="Transaction description")
    batch_number = models.CharField(max_length=50, blank=True, help_text="Batch/lot number")
    
    # Audit trail
    created_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name='stock_transactions_created')
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'stock_transactions'
        ordering = ['-txn_date', '-created_at']
        indexes = [
            models.Index(fields=['product', 'warehouse']),
            models.Index(fields=['reference_type', 'reference_id']),
            models.Index(fields=['txn_date']),
        ]
    
    def __str__(self):
        return f"{self.transaction_type}: {self.product.name} ({self.quantity}) @ {self.warehouse.name}"
    
    def save(self, *args, **kwargs):
        # Calculate total cost
        self.total_cost = self.quantity * self.unit_cost
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.product.name} - {self.transaction_type} - {self.quantity} @ {self.warehouse.name}"

    @classmethod
    def create_sale_transaction(cls, product_id, warehouse_id, quantity, unit_cost, invoice_id, invoice_number, user=None):
        """
        Create a stock transaction for sales/invoice
        """
        from django.utils import timezone

        return cls.objects.create(
            product_id=product_id,
            warehouse_id=warehouse_id,
            transaction_type='SALE',
            reference_type='INV',
            reference_id=invoice_id,
            quantity=-Decimal(str(quantity)),  # Negative for outbound
            unit_cost=Decimal(str(unit_cost)),
            txn_date=timezone.now().date(),
            description=f"Sold - Invoice {invoice_number}",
            created_by=user
        )


class GoodsReceiptNote(models.Model):
    """
    Goods Receipt Notes for receiving purchased items
    Following ERD: grns table
    """
    grn_id = models.AutoField(primary_key=True)
    grn_number = models.CharField(max_length=50, unique=True, help_text="GRN reference number")
    
    # Links to purchase order
    purchase_order = models.ForeignKey(
        'purchase.PurchaseOrder',
        on_delete=models.CASCADE,
        related_name='goods_receipts'
    )
    
    # Warehouse and receiving details
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, related_name='goods_receipts')
    received_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name='goods_received')
    receipt_date = models.DateField(default=date.today)
    
    # Status and notes
    status = models.CharField(
        max_length=20,
        choices=[
            ('DRAFT', 'Draft'),
            ('RECEIVED', 'Received'),
            ('POSTED', 'Posted to Inventory'),
            ('CANCELLED', 'Cancelled'),
        ],
        default='DRAFT'
    )
    
    notes = models.TextField(blank=True, help_text="Receipt notes and comments")
    
    # Totals
    total_quantity = models.DecimalField(max_digits=15, decimal_places=4, default=Decimal('0.0000'))
    total_value = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    
    # Audit trail
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    posted_at = models.DateTimeField(null=True, blank=True)
    posted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='grns_posted'
    )
    
    class Meta:
        db_table = 'grns'
        ordering = ['-receipt_date', '-created_at']
    
    def __str__(self):
        return f"GRN {self.grn_number} - PO {self.purchase_order.po_number}"
    
    def save(self, *args, **kwargs):
        # Auto-generate GRN number if not provided
        if not self.grn_number:
            last_grn = GoodsReceiptNote.objects.filter(
                grn_number__startswith='GRN-'
            ).order_by('-created_at').first()
            
            if last_grn:
                try:
                    last_number = int(last_grn.grn_number.split('-')[1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1
            
            self.grn_number = f'GRN-{new_number:06d}'
        
        super().save(*args, **kwargs)


class GoodsReceiptNoteItem(models.Model):
    """
    Individual items in a Goods Receipt Note
    Following ERD: grn_items table
    """
    grn_item_id = models.AutoField(primary_key=True)
    grn = models.ForeignKey(GoodsReceiptNote, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey('sales.Product', on_delete=models.CASCADE, related_name='grn_items', null=True, blank=True)
    
    # Quantities
    quantity_ordered = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        help_text="Quantity originally ordered"
    )
    quantity_received = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        help_text="Quantity actually received"
    )
    
    # Cost information
    unit_cost = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        help_text="Cost per unit as per purchase order"
    )
    total_cost = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Total cost for this line item"
    )
    
    # Quality and condition
    condition = models.CharField(
        max_length=20,
        choices=[
            ('GOOD', 'Good Condition'),
            ('DAMAGED', 'Damaged'),
            ('EXPIRED', 'Expired'),
            ('REJECTED', 'Rejected'),
        ],
        default='GOOD'
    )
    
    # Additional details
    batch_number = models.CharField(max_length=50, blank=True)
    expiry_date = models.DateField(null=True, blank=True)
    notes = models.TextField(blank=True)
    
    # Line order
    line_order = models.PositiveIntegerField(default=0)
    
    class Meta:
        db_table = 'grn_items'
        ordering = ['line_order']
        # Remove unique constraint since product can be null
        # unique_together = ['grn', 'product']
    
    def __str__(self):
        product_name = self.product.name if self.product else "Unlinked Item"
        return f"{self.grn.grn_number} - {product_name} ({self.quantity_received})"
    
    def save(self, *args, **kwargs):
        # Calculate total cost
        self.total_cost = self.quantity_received * self.unit_cost
        super().save(*args, **kwargs)
    
    def create_stock_transaction(self):
        """Create stock transaction when GRN item is posted (only for items with product links)"""
        if self.product and self.quantity_received > 0 and self.condition == 'GOOD':
            StockTransaction.objects.create(
                product=self.product,
                warehouse=self.grn.warehouse,
                transaction_type='RECEIPT',
                reference_type='GRN',
                reference_id=self.grn.grn_id,
                quantity=self.quantity_received,
                unit_cost=self.unit_cost,
                txn_date=self.grn.receipt_date,
                description=f"Goods receipt from PO {self.grn.purchase_order.po_number}",
                batch_number=self.batch_number,
                created_by=self.grn.received_by
            )
    
    def create_journal_entry(self):
        """
        Create GL journal entry for inventory receipt with proper input tax handling
        
        Creates journal entry in DRAFT status for manual review and posting.
        This ensures proper segregation of duties and allows for review
        before the entry affects the general ledger.
        
        For items with input tax, creates entries like:
        - Product Account: 90.00 (Debit)
        - Input Tax Account: 10.00 (Debit) 
        - Accounts Payable: 100.00 (Credit)
        
        Only creates journal entries for items with product links.
        """
        if not self.product or self.quantity_received <= 0 or self.condition != 'GOOD':
            return None
            
        try:
            from gl.models import JournalEntry, JournalEntryLine, Account
            from django.db import transaction
            from decimal import Decimal
            
            # Get PO line item for tax information
            po = self.grn.purchase_order
            po_line_item = None
            
            # Find the corresponding PO line item for this product
            try:
                po_line_item = po.line_items.filter(product=self.product).first()
            except:
                pass
            
            # Calculate amounts
            base_amount = self.quantity_received * self.unit_cost  # Product cost excluding tax
            tax_amount = Decimal('0.00')
            total_amount = base_amount
            
            # Calculate tax if applicable
            if po_line_item and po_line_item.taxable and po_line_item.tax_rate > 0:
                # Calculate tax on the received portion
                received_ratio = self.quantity_received / po_line_item.quantity if po_line_item.quantity > 0 else Decimal('1.00')
                
                # Method 1: Use proportional tax from PO line item
                if po_line_item.tax_amount > 0:
                    tax_amount = (po_line_item.tax_amount * received_ratio).quantize(Decimal('0.01'))
                else:
                    # Method 2: Calculate tax directly on received amount
                    tax_amount = (base_amount * po_line_item.tax_rate / 100).quantize(Decimal('0.01'))
                
                total_amount = base_amount + tax_amount
            
            # Get functional currency from company settings
            from account.models import Company
            try:
                company = Company.objects.first()
                if company and company.functional_currency:
                    # Map currency codes to symbols
                    currency_map = {
                        'USD': '$', 'EUR': '€', 'GBP': '£', 'INR': '₹', 
                        'JPY': '¥', 'CNY': '¥', 'AUD': '$', 'CAD': '$'
                    }
                    currency_symbol = currency_map.get(company.functional_currency, company.functional_currency)
                else:
                    currency_symbol = '$'
            except:
                currency_symbol = '$'  # Default fallback
            
            # Helper function to format currency with proper decimal places
            def format_currency(amount):
                """Format currency with functional currency symbol and 2 decimal places"""
                return f"{currency_symbol}{amount:,.2f}"
            
            # Create comprehensive description with tax details
            if po_line_item:
                if tax_amount > 0:
                    description = (
                        f"Inventory Receipt: {self.product.name} | "
                        f"GRN: {self.grn.grn_number} (Received: {self.quantity_received:,.4f} units @ {format_currency(self.unit_cost)}/unit) | "
                        f"PO: {po.po_number} (Ordered: {po_line_item.quantity:,.4f} units @ {format_currency(po_line_item.unit_price)}/unit, Tax: {po_line_item.tax_rate:.2f}%) | "
                        f"Product: {format_currency(base_amount)}, Input Tax: {format_currency(tax_amount)}, Total: {format_currency(total_amount)}"
                    )
                else:
                    description = (
                        f"Inventory Receipt: {self.product.name} | "
                        f"GRN: {self.grn.grn_number} (Received: {self.quantity_received:,.4f} units @ {format_currency(self.unit_cost)}/unit) | "
                        f"PO: {po.po_number} (Ordered: {po_line_item.quantity:,.4f} units @ {format_currency(po_line_item.unit_price)}/unit) | "
                        f"Total: {format_currency(total_amount)}"
                    )
            else:
                description = (
                    f"Inventory Receipt: {self.product.name} | "
                    f"GRN: {self.grn.grn_number} (Received: {self.quantity_received:,.4f} units @ {format_currency(self.unit_cost)}/unit) | "
                    f"PO: {po.po_number} | "
                    f"Total: {format_currency(total_amount)}"
                )
            
            with transaction.atomic():
                # Create journal entry for inventory receipt (in DRAFT status for manual review)
                journal_entry = JournalEntry.objects.create(
                    entry_number=f"GRN-{self.grn.grn_number}-{self.grn_item_id}",
                    reference_number=f"GRN-{self.grn.grn_number}",
                    description=description,
                    net_amount=total_amount,
                    entry_type='GENERAL',
                    transaction_date=self.grn.receipt_date,
                    status=JournalEntry.DRAFT,
                    created_by=self.grn.received_by,
                    # Source document tracking for audit trail
                    source_document_type='GRN',
                    source_document_id=self.grn.grn_number,
                    source_document_reference=f"PO-{self.grn.purchase_order.po_number}"
                )
                
                line_number = 1
                
                # Debit: Inventory Asset Account (product cost excluding tax)
                inventory_account = self.product.inventory_asset_account_gl
                if inventory_account:
                    JournalEntryLine.objects.create(
                        journal_entry=journal_entry,
                        line_number=line_number,
                        account=inventory_account,
                        description=f"Inventory receipt - {self.product.name}",
                        debit_amount=base_amount,
                        credit_amount=Decimal('0.00')
                    )
                    line_number += 1
                
                # Debit: Input Tax Account (if applicable)
                if tax_amount > 0:
                    try:
                        # Try to find a specific Input Tax account
                        input_tax_account = Account.objects.filter(
                            account_name__icontains='Input Tax'
                        ).first()
                        
                        if not input_tax_account:
                            # Fallback to a general tax account
                            input_tax_account = Account.objects.filter(
                                account_name__icontains='Tax'
                            ).first()
                        
                        if input_tax_account:
                            JournalEntryLine.objects.create(
                                journal_entry=journal_entry,
                                line_number=line_number,
                                account=input_tax_account,
                                description=f"Input tax on purchase - {self.product.name}",
                                debit_amount=tax_amount,
                                credit_amount=Decimal('0.00')
                            )
                            line_number += 1
                        else:
                            print(f"Warning: No Input Tax account found for GRN {self.grn.grn_number}")
                            # Add tax to base amount if no tax account found
                            journal_entry.lines.filter(line_number=1).update(
                                debit_amount=total_amount,
                                description=f"Inventory Receipt: {self.product.name} - Product Cost (including tax)"
                            )
                    except Exception as e:
                        print(f"Warning: Error creating input tax entry for GRN {self.grn.grn_number}: {e}")
                        # Add tax to base amount if tax processing fails
                        journal_entry.lines.filter(line_number=1).update(
                            debit_amount=total_amount,
                            description=f"Inventory Receipt: {self.product.name} - Product Cost (including tax)"
                        )
                
                # Credit: Accounts Payable (total amount including tax)
                # Try to use vendor-specific payable account or fall back to expense account
                credit_account = None
                
                # First, try to find a general Accounts Payable account
                try:
                    credit_account = Account.objects.filter(
                        account_name__icontains='Accounts Payable'
                    ).first()
                except:
                    pass
                
                # Fallback to product's expense account
                if not credit_account:
                    credit_account = self.product.expense_account_gl
                
                if credit_account:
                    vendor_name = po.vendor.display_name if po.vendor else 'Vendor'
                    JournalEntryLine.objects.create(
                        journal_entry=journal_entry,
                        line_number=line_number,
                        account=credit_account,
                        description=f"Purchase from {vendor_name} - {self.product.name}",
                        debit_amount=Decimal('0.00'),
                        credit_amount=total_amount
                    )
                
                print(f"✅ Created journal entry {journal_entry.entry_number} for GRN item {self.grn_item_id}")
                return journal_entry
                
        except Exception as e:
            print(f"❌ Failed to create journal entry for GRN item {self.grn_item_id}: {str(e)}")
            return None

    def update_inventory(self):
        """Update inventory levels when GRN item is posted"""
        if self.quantity_received > 0 and self.condition == 'GOOD':
            inventory, created = Inventory.objects.get_or_create(
                product=self.product,
                warehouse=self.grn.warehouse,
                defaults={
                    'quantity_on_hand': Decimal('0.0000'),
                    'average_cost': self.unit_cost,
                    'last_cost': self.unit_cost,
                }
            )
            
            # Update quantities
            old_quantity = inventory.quantity_on_hand
            old_cost = inventory.average_cost
            
            # Calculate new weighted average cost
            if old_quantity > 0:
                total_cost = (old_quantity * old_cost) + (self.quantity_received * self.unit_cost)
                new_quantity = old_quantity + self.quantity_received
                inventory.average_cost = total_cost / new_quantity
            else:
                inventory.average_cost = self.unit_cost
            
            inventory.quantity_on_hand += self.quantity_received
            inventory.last_cost = self.unit_cost
            inventory.last_transaction_date = self.grn.receipt_date
            inventory.save()
            
            # Journal entry will be created when bill is generated manually
            # self.create_journal_entry()


class VendorInvoice(models.Model):
    """
    Vendor Invoices for purchase transactions
    Following ERD: vendor_invoices table
    """
    vendor_invoice_id = models.AutoField(primary_key=True)
    purchase_order = models.ForeignKey(
        'purchase.PurchaseOrder',
        on_delete=models.CASCADE,
        related_name='vendor_invoices'
    )
    
    # Invoice details
    invoice_number = models.CharField(max_length=100, help_text="Vendor's invoice number")
    invoice_date = models.DateField()
    due_date = models.DateField()
    
    # Financial information
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    total_amount = models.DecimalField(max_digits=15, decimal_places=2)
    
    # Status
    status = models.CharField(
        max_length=20,
        choices=[
            ('RECEIVED', 'Received'),
            ('VERIFIED', 'Verified'),
            ('APPROVED', 'Approved'),
            ('PAID', 'Paid'),
            ('DISPUTED', 'Disputed'),
            ('CANCELLED', 'Cancelled'),
        ],
        default='RECEIVED'
    )
    
    # Payment tracking
    amount_paid = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    balance_due = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    
    # Additional information
    description = models.TextField(blank=True)
    notes = models.TextField(blank=True)
    
    # Audit trail
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name='vendor_invoices_created')
    
    class Meta:
        db_table = 'vendor_invoices'
        ordering = ['-invoice_date', '-created_at']
        unique_together = ['purchase_order', 'invoice_number']
    
    def __str__(self):
        return f"Invoice {self.invoice_number} - PO {self.purchase_order.po_number}"
    
    def save(self, *args, **kwargs):
        # Calculate balance due
        self.balance_due = self.total_amount - self.amount_paid
        super().save(*args, **kwargs)


class GoodsReturnNote(models.Model):
    """
    Goods Return Notes for returning items to vendors
    Following ERD pattern similar to GRN
    """
    grn_return_id = models.AutoField(primary_key=True)
    grn_return_number = models.CharField(max_length=50, unique=True, help_text="GRN Return reference number")
    
    # Links to original GRN and vendor
    original_grn = models.ForeignKey(
        GoodsReceiptNote,
        on_delete=models.CASCADE,
        related_name='return_notes',
        help_text="Original GRN being returned"
    )
    vendor = models.ForeignKey(
        'contacts.Contact',  # Using Contact model
        on_delete=models.SET_NULL,  # Allow null for existing records
        null=True,
        blank=True,
        related_name='goods_returns',
        help_text="Vendor to return goods to (from contacts system)"
    )
    
    # Warehouse and return details
    warehouse = models.ForeignKey(
        'inventory.Warehouse',
        on_delete=models.CASCADE,
        related_name='goods_returns'
    )
    returned_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='goods_returned'
    )
    return_date = models.DateField(default=date.today)
    
    # Return reason
    return_reason = models.CharField(
        max_length=50,
        choices=[
            ('DEFECTIVE', 'Defective/Damaged'),
            ('WRONG_ITEM', 'Wrong Item Delivered'),
            ('EXCESS_QTY', 'Excess Quantity'),
            ('QUALITY_ISSUE', 'Quality Issue'),
            ('NOT_ORDERED', 'Not Ordered'),
            ('EXPIRED', 'Expired'),
            ('OTHER', 'Other')
        ],
        help_text="Reason for return"
    )
    
    # Status and notes
    status = models.CharField(
        max_length=20,
        choices=[
            ('DRAFT', 'Draft'),
            ('APPROVED', 'Approved'),
            ('RETURNED', 'Returned to Vendor'),
            ('POSTED', 'Posted to Inventory'),
            ('CANCELLED', 'Cancelled')
        ],
        default='DRAFT'
    )
    
    notes = models.TextField(blank=True, help_text="Return notes and comments")
    
    # Totals
    total_quantity = models.DecimalField(max_digits=15, decimal_places=4, default=Decimal('0.0000'))
    total_value = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    
    # Credit/Refund information
    expected_credit_amount = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        default=Decimal('0.00'),
        help_text="Expected credit from vendor"
    )
    actual_credit_amount = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        default=Decimal('0.00'),
        help_text="Actual credit received from vendor"
    )
    credit_received_date = models.DateField(null=True, blank=True)
    
    # Audit trail
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    approved_at = models.DateTimeField(null=True, blank=True)
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='grn_returns_approved'
    )
    posted_at = models.DateTimeField(null=True, blank=True)
    posted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='grn_returns_posted'
    )
    
    class Meta:
        db_table = 'grn_returns'
        ordering = ['-return_date', '-created_at']
    
    def __str__(self):
        vendor_name = self.vendor.name if self.vendor else "No Vendor"
        return f"RET {self.grn_return_number} - {vendor_name}"
    
    def save(self, *args, **kwargs):
        # Auto-generate return number if not provided
        if not self.grn_return_number:
            last_return = GoodsReturnNote.objects.filter(
                grn_return_number__startswith='RET-'
            ).order_by('-created_at').first()
            
            if last_return:
                try:
                    last_number = int(last_return.grn_return_number.split('-')[1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1
            
            self.grn_return_number = f'RET-{new_number:06d}'
        
        super().save(*args, **kwargs)


class GoodsReturnNoteItem(models.Model):
    """
    Individual items in a Goods Return Note
    Following ERD pattern similar to GRN items
    """
    grn_return_item_id = models.AutoField(primary_key=True)
    grn_return = models.ForeignKey(GoodsReturnNote, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey('sales.Product', on_delete=models.CASCADE, related_name='grn_return_items')
    
    # Original GRN item reference for traceability
    original_grn_item = models.ForeignKey(
        GoodsReceiptNoteItem,
        on_delete=models.CASCADE,
        related_name='return_items',
        help_text="Original GRN item being returned"
    )
    
    # Quantities
    quantity_received = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        help_text="Quantity originally received"
    )
    quantity_returned = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        help_text="Quantity being returned"
    )
    
    # Cost information
    unit_cost = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        help_text="Original cost per unit"
    )
    return_value = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Total return value for this line item"
    )
    
    # Return specific details
    return_reason = models.CharField(
        max_length=50,
        choices=[
            ('DEFECTIVE', 'Defective/Damaged'),
            ('WRONG_ITEM', 'Wrong Item Delivered'),
            ('EXCESS_QTY', 'Excess Quantity'),
            ('QUALITY_ISSUE', 'Quality Issue'),
            ('NOT_ORDERED', 'Not Ordered'),
            ('EXPIRED', 'Expired'),
            ('OTHER', 'Other'),
        ],
        help_text="Specific reason for this item return"
    )
    
    # Additional details
    batch_number = models.CharField(max_length=50, blank=True)
    condition_notes = models.TextField(blank=True, help_text="Notes about item condition")
    photos_attached = models.BooleanField(default=False, help_text="Whether photos are attached for evidence")
    
    # Line order
    line_order = models.PositiveIntegerField(default=0)
    
    class Meta:
        db_table = 'grn_return_items'
        ordering = ['line_order']
        unique_together = ['grn_return', 'product', 'original_grn_item']
    
    def __str__(self):
        return f"{self.grn_return.grn_return_number} - {self.product.name} ({self.quantity_returned})"
    
    def save(self, *args, **kwargs):
        # Calculate return value
        self.return_value = self.quantity_returned * self.unit_cost
        super().save(*args, **kwargs)
    
    def create_stock_transaction(self):
        """Create stock transaction when return item is posted"""
        if self.quantity_returned > 0:
            StockTransaction.objects.create(
                product=self.product,
                warehouse=self.grn_return.warehouse,
                transaction_type='RETURN',
                reference_type='RET',
                reference_id=self.grn_return.grn_return_id,
                quantity=-self.quantity_returned,  # Negative for return
                unit_cost=self.unit_cost,
                txn_date=self.grn_return.return_date,
                description=f"Goods return to {self.grn_return.vendor.name} - {self.return_reason}",
                batch_number=self.batch_number,
                created_by=self.grn_return.returned_by
            )
    
    def create_journal_entry(self):
        """
        Create GL journal entry for inventory return with proper tax handling
        
        Creates journal entry in DRAFT status for manual review and posting.
        This creates the reverse of GRN entries for proper accounting.
        
        For returns with tax, creates entries like:
        - Accounts Payable (Vendor): XX.XX (Debit) - Gross amount including tax
        - Inventory Asset Account: XX.XX (Credit) - Product cost excluding tax
        - Sales Tax Payable: XX.XX (Credit) - Tax amount to be refunded/adjusted
        
        Accounts are left blank for user to fill as per your requirements.
        """
        if self.quantity_returned <= 0:
            return None
            
        try:
            from gl.models import JournalEntry, JournalEntryLine, Account
            from django.db import transaction
            from decimal import Decimal
            
            # Get original PO line item for tax information
            po = self.grn_return.original_grn.purchase_order
            po_line_item = None
            
            # Find the corresponding PO line item for this product
            try:
                po_line_item = po.line_items.filter(product=self.product).first()
            except:
                pass
            
            # Calculate amounts (same logic as GRN but reversed)
            base_amount = self.quantity_returned * self.unit_cost  # Product cost excluding tax
            tax_amount = Decimal('0.00')
            total_amount = base_amount
            
            # Calculate tax if applicable (reverse of GRN calculation)
            if po_line_item and po_line_item.taxable and po_line_item.tax_rate > 0:
                # Calculate tax on the returned portion
                returned_ratio = self.quantity_returned / self.original_grn_item.quantity_received if self.original_grn_item.quantity_received > 0 else Decimal('1.00')
                
                # Method 1: Use proportional tax from original GRN item
                if hasattr(po_line_item, 'tax_amount') and po_line_item.tax_amount > 0:
                    # Calculate tax proportionally based on returned quantity
                    original_received_ratio = self.original_grn_item.quantity_received / po_line_item.quantity if po_line_item.quantity > 0 else Decimal('1.00')
                    original_tax_for_received = (po_line_item.tax_amount * original_received_ratio).quantize(Decimal('0.01'))
                    tax_amount = (original_tax_for_received * returned_ratio).quantize(Decimal('0.01'))
                else:
                    # Method 2: Calculate tax directly on returned amount
                    tax_amount = (base_amount * po_line_item.tax_rate / 100).quantize(Decimal('0.01'))
                
                total_amount = base_amount + tax_amount
            
            # Get functional currency from company settings
            from account.models import Company
            try:
                company = Company.objects.first()
                if company and company.functional_currency:
                    # Map currency codes to symbols
                    currency_map = {
                        'USD': '$', 'EUR': '€', 'GBP': '£', 'INR': '₹', 
                        'JPY': '¥', 'CNY': '¥', 'AUD': '$', 'CAD': '$'
                    }
                    currency_symbol = currency_map.get(company.functional_currency, company.functional_currency)
                else:
                    currency_symbol = '$'
            except:
                currency_symbol = '$'  # Default fallback
            
            # Helper function to format currency with proper decimal places
            def format_currency(amount):
                """Format currency with functional currency symbol and 2 decimal places"""
                return f"{currency_symbol}{amount:,.2f}"
            
            # Create comprehensive description with tax details (similar to GRN but for returns)
            if po_line_item:
                if tax_amount > 0:
                    description = (
                        f"Goods return to {self.grn_return.vendor.name}: {self.product.name} | "
                        f"Return: {self.grn_return.grn_return_number} (Returned: {self.quantity_returned:,.4f} units @ {format_currency(self.unit_cost)}/unit) | "
                        f"Original GRN: {self.grn_return.original_grn.grn_number} | "
                        f"PO: {po.po_number} (Tax: {po_line_item.tax_rate:.2f}%) | "
                        f"Reason: {self.get_return_reason_display()} | "
                        f"Product: {format_currency(base_amount)}, Tax: {format_currency(tax_amount)}, Total: {format_currency(total_amount)}"
                    )
                else:
                    description = (
                        f"Goods return to {self.grn_return.vendor.name}: {self.product.name} | "
                        f"Return: {self.grn_return.grn_return_number} (Returned: {self.quantity_returned:,.4f} units @ {format_currency(self.unit_cost)}/unit) | "
                        f"Original GRN: {self.grn_return.original_grn.grn_number} | "
                        f"PO: {po.po_number} | "
                        f"Reason: {self.get_return_reason_display()} | "
                        f"Total: {format_currency(total_amount)}"
                    )
            else:
                description = (
                    f"Goods return to {self.grn_return.vendor.name}: {self.product.name} | "
                    f"Return: {self.grn_return.grn_return_number} (Returned: {self.quantity_returned:,.4f} units @ {format_currency(self.unit_cost)}/unit) | "
                    f"Original GRN: {self.grn_return.original_grn.grn_number} | "
                    f"Reason: {self.get_return_reason_display()} | "
                    f"Total: {format_currency(total_amount)}"
                )
            
            with transaction.atomic():
                # Create journal entry for inventory return (in DRAFT status for manual review)
                journal_entry = JournalEntry.objects.create(
                    entry_number=f"RET-{self.grn_return.grn_return_number}-{self.grn_return_item_id}",
                    reference_number=f"RET-{self.grn_return.grn_return_number}",
                    description=description,
                    net_amount=total_amount,
                    entry_type='GENERAL',
                    transaction_date=self.grn_return.return_date,
                    status='DRAFT',
                    created_by=self.grn_return.returned_by,
                    # Source document tracking for audit trail
                    source_document_type='RET',
                    source_document_id=self.grn_return.grn_return_number,
                    source_document_reference=f"GRN-{self.grn_return.original_grn.grn_number}"
                )
                
                line_number = 1
                
                # Find default accounts for journal entries
                accounts_payable_account = None
                inventory_account = None
                tax_account = None
                
                try:
                    # Try to find appropriate accounts
                    accounts_payable_account = Account.objects.filter(account_name__icontains='Accounts Payable').first()
                    inventory_account = Account.objects.filter(account_name__icontains='Inventory').first()
                    tax_account = Account.objects.filter(account_name__icontains='Sales Tax Payable').first()
                except:
                    pass
                
                # Debit: Accounts Payable (Vendor) - Gross amount including tax
                JournalEntryLine.objects.create(
                    journal_entry=journal_entry,
                    line_number=line_number,
                    account=accounts_payable_account,
                    description=f"Return credit due from {self.grn_return.vendor.name} - {self.product.name} (Gross Amount)",
                    debit_amount=total_amount,
                    credit_amount=Decimal('0.00')
                )
                line_number += 1
                
                # Credit: Inventory Asset Account - Product cost excluding tax
                JournalEntryLine.objects.create(
                    journal_entry=journal_entry,
                    line_number=line_number,
                    account=inventory_account,
                    description=f"Inventory return - {self.product.name} (Product Cost)",
                    debit_amount=Decimal('0.00'),
                    credit_amount=base_amount
                )
                line_number += 1
                
                # Credit: Sales Tax Payable (if applicable) - Tax amount
                if tax_amount > 0:
                    JournalEntryLine.objects.create(
                        journal_entry=journal_entry,
                        line_number=line_number,
                        account=tax_account,
                        description=f"Sales tax adjustment on return - {self.product.name} (Tax Amount)",
                        debit_amount=Decimal('0.00'),
                        credit_amount=tax_amount
                    )
                
                print(f"✅ Created comprehensive journal entry {journal_entry.entry_number} for return item {self.grn_return_item_id}")
                print(f"   • Total Amount: {format_currency(total_amount)} (Product: {format_currency(base_amount)}, Tax: {format_currency(tax_amount)})")
                # Count lines - we created 2 or 3 lines depending on tax
                line_count = 3 if tax_amount > 0 else 2
                print(f"   • Lines: {line_count} (Debit: Accounts Payable, Credit: Inventory + Tax)")
                return journal_entry
                
        except Exception as e:
            print(f"❌ Failed to create journal entry for return item {self.grn_return_item_id}: {str(e)}")
            return None

    def update_inventory(self):
        """Update inventory levels when return item is posted"""
        if self.quantity_returned > 0:
            try:
                inventory = Inventory.objects.get(
                    product=self.product,
                    warehouse=self.grn_return.warehouse
                )
                
                # Reduce inventory quantity
                inventory.quantity_on_hand -= self.quantity_returned
                inventory.last_transaction_date = self.grn_return.return_date
                inventory.save()
                
                # Journal entry will be created when bill is generated manually
                # self.create_journal_entry()
                
            except Inventory.DoesNotExist:
                print(f"⚠️ No inventory record found for product {self.product.name} in warehouse {self.grn_return.warehouse.name}")


# Inventory Transfer Models
class InventoryTransfer(models.Model):
    """
    Inventory transfers between warehouses
    Internal transfers with no financial impact
    """
    transfer_id = models.AutoField(primary_key=True)
    transfer_number = models.CharField(max_length=50, unique=True, help_text="Transfer reference number")
    
    # Warehouse details
    from_warehouse = models.ForeignKey(
        Warehouse, 
        on_delete=models.CASCADE, 
        related_name='transfers_out',
        help_text="Source warehouse"
    )
    to_warehouse = models.ForeignKey(
        Warehouse, 
        on_delete=models.CASCADE, 
        related_name='transfers_in',
        help_text="Destination warehouse"
    )
    
    # Transfer details
    transfer_date = models.DateField(default=date.today)
    notes = models.TextField(blank=True, help_text="Transfer notes and comments")
    
    # Status
    status = models.CharField(
        max_length=20,
        choices=[
            ('DRAFT', 'Draft'),
            ('PENDING', 'Pending Transfer'),
            ('IN_TRANSIT', 'In Transit'),
            ('COMPLETED', 'Completed'),
            ('CANCELLED', 'Cancelled'),
        ],
        default='DRAFT'
    )
    
    # Totals
    total_quantity = models.DecimalField(max_digits=15, decimal_places=4, default=Decimal('0.0000'))
    total_items = models.PositiveIntegerField(default=0, help_text="Number of different items")
    
    # Audit trail
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name='transfers_created')
    completed_at = models.DateTimeField(null=True, blank=True)
    completed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='transfers_completed'
    )
    
    class Meta:
        db_table = 'inventory_transfers'
        ordering = ['-transfer_date', '-created_at']
    
    def __str__(self):
        return f"{self.transfer_number}: {self.from_warehouse.name} → {self.to_warehouse.name}"
    
    def save(self, *args, **kwargs):
        # Auto-generate transfer number if not provided
        if not self.transfer_number:
            # Get the next transfer number
            last_transfer = InventoryTransfer.objects.filter(
                transfer_number__startswith='TRF-'
            ).order_by('-transfer_id').first()
            
            if last_transfer:
                # Extract number from last transfer and increment
                try:
                    last_number = int(last_transfer.transfer_number.split('-')[1])
                    next_number = last_number + 1
                except (ValueError, IndexError):
                    next_number = 1
            else:
                next_number = 1
            
            self.transfer_number = f"TRF-{next_number:06d}"
        
        super().save(*args, **kwargs)
    
    def calculate_totals(self):
        """Calculate and update totals from transfer items"""
        items = self.items.all()
        total_qty = sum(item.quantity for item in items)
        total_items = items.count()
        
        self.total_quantity = total_qty
        self.total_items = total_items
        self.save()
    
    def complete_transfer(self, user):
        """Complete the transfer and create stock transactions"""
        if self.status != 'PENDING':
            raise ValueError("Only pending transfers can be completed")
        
        # Create stock transactions for each item
        for item in self.items.all():
            item.create_stock_transactions()
        
        # Update transfer status
        self.status = 'COMPLETED'
        self.completed_at = timezone.now()
        self.completed_by = user
        self.save()


class InventoryTransferItem(models.Model):
    """
    Individual items in an inventory transfer
    """
    transfer_item_id = models.AutoField(primary_key=True)
    transfer = models.ForeignKey(InventoryTransfer, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey('sales.Product', on_delete=models.CASCADE, related_name='transfer_items')
    
    # Quantity
    quantity = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        help_text="Quantity to transfer"
    )
    
    # Additional details
    batch_number = models.CharField(max_length=50, blank=True)
    notes = models.TextField(blank=True)
    
    # Line order
    line_order = models.PositiveIntegerField(default=0)
    
    class Meta:
        db_table = 'inventory_transfer_items'
        ordering = ['line_order']
        unique_together = ['transfer', 'product', 'batch_number']
    
    def __str__(self):
        return f"{self.transfer.transfer_number} - {self.product.name} ({self.quantity})"
    
    def create_stock_transactions(self):
        """Create stock transactions for both source and destination warehouses"""
        if self.quantity <= 0:
            return
        
        # Get average cost from source warehouse
        try:
            source_inventory = Inventory.objects.get(
                product=self.product,
                warehouse=self.transfer.from_warehouse
            )
            unit_cost = source_inventory.average_cost
        except Inventory.DoesNotExist:
            unit_cost = Decimal('0.0000')
        
        # Create outbound transaction (negative quantity)
        StockTransaction.objects.create(
            product=self.product,
            warehouse=self.transfer.from_warehouse,
            transaction_type='TRANSFER',
            reference_type='TRF',
            reference_id=self.transfer.transfer_id,
            quantity=-self.quantity,  # Negative for outbound
            unit_cost=unit_cost,
            txn_date=self.transfer.transfer_date,
            description=f"Transfer out to {self.transfer.to_warehouse.name} - {self.transfer.transfer_number}",
            batch_number=self.batch_number,
            created_by=self.transfer.created_by
        )
        
        # Create inbound transaction (positive quantity)
        StockTransaction.objects.create(
            product=self.product,
            warehouse=self.transfer.to_warehouse,
            transaction_type='TRANSFER',
            reference_type='TRF',
            reference_id=self.transfer.transfer_id,
            quantity=self.quantity,  # Positive for inbound
            unit_cost=unit_cost,
            txn_date=self.transfer.transfer_date,
            description=f"Transfer in from {self.transfer.from_warehouse.name} - {self.transfer.transfer_number}",
            batch_number=self.batch_number,
            created_by=self.transfer.created_by
        )
        
        # Update inventory levels
        self.update_inventory_levels(unit_cost)
    
    def update_inventory_levels(self, unit_cost):
        """Update inventory levels in both warehouses"""
        # Update source warehouse (decrease)
        source_inventory, created = Inventory.objects.get_or_create(
            product=self.product,
            warehouse=self.transfer.from_warehouse,
            defaults={
                'quantity_on_hand': Decimal('0.0000'),
                'average_cost': unit_cost,
                'last_cost': unit_cost
            }
        )
        source_inventory.quantity_on_hand -= self.quantity
        source_inventory.last_transaction_date = timezone.now()
        source_inventory.save()
        
        # Update destination warehouse (increase)
        dest_inventory, created = Inventory.objects.get_or_create(
            product=self.product,
            warehouse=self.transfer.to_warehouse,
            defaults={
                'quantity_on_hand': Decimal('0.0000'),
                'average_cost': unit_cost,
                'last_cost': unit_cost
            }
        )
        dest_inventory.quantity_on_hand += self.quantity
        dest_inventory.last_transaction_date = timezone.now()
        dest_inventory.save() 
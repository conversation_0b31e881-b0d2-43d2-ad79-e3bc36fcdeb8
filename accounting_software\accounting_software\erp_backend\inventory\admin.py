"""
Enterprise Inventory Admin
Following our established patterns with comprehensive management
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from .models import (
    Warehouse, StockLevel, StockTransaction, StockAdjustment, StockAdjustmentItem,
    StockTransfer, StockTransferItem, StockValuation, StockValuationItem
)


@admin.register(Warehouse)
class WarehouseAdmin(admin.ModelAdmin):
    list_display = [
        'code', 'name', 'warehouse_type', 'city', 'state',
        'is_active', 'is_default', 'created_at'
    ]
    list_filter = ['warehouse_type', 'is_active', 'is_default', 'state', 'country']
    search_fields = ['code', 'name', 'city', 'address']
    ordering = ['code']

    fieldsets = (
        ('Basic Information', {
            'fields': ('code', 'name', 'warehouse_type', 'manager')
        }),
        ('Location Details', {
            'fields': ('address', 'city', 'state', 'postal_code', 'country')
        }),
        ('Contact Information', {
            'fields': ('phone', 'email')
        }),
        ('Settings', {
            'fields': ('is_active', 'allow_negative_stock', 'is_default')
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(StockLevel)
class StockLevelAdmin(admin.ModelAdmin):
    list_display = [
        'product_code', 'product_name', 'warehouse_code', 'quantity_on_hand',
        'quantity_reserved', 'available_quantity', 'reorder_point', 'stock_status',
        'last_updated'
    ]
    list_filter = ['warehouse', 'warehouse__warehouse_type', 'last_counted_date']
    search_fields = ['product__code', 'product__name', 'warehouse__code', 'warehouse__name']
    ordering = ['product__code', 'warehouse__code']

    readonly_fields = ['last_updated', 'available_quantity']

    def product_code(self, obj):
        return obj.product.code
    product_code.short_description = 'Product Code'
    product_code.admin_order_field = 'product__code'

    def product_name(self, obj):
        return obj.product.name
    product_name.short_description = 'Product Name'
    product_name.admin_order_field = 'product__name'

    def warehouse_code(self, obj):
        return obj.warehouse.code
    warehouse_code.short_description = 'Warehouse'
    warehouse_code.admin_order_field = 'warehouse__code'

    def available_quantity(self, obj):
        return obj.available_quantity
    available_quantity.short_description = 'Available'

    def stock_status(self, obj):
        if obj.is_out_of_stock:
            return format_html('<span style="color: red;">Out of Stock</span>')
        elif obj.is_below_reorder_point:
            return format_html('<span style="color: orange;">Low Stock</span>')
        else:
            return format_html('<span style="color: green;">In Stock</span>')
    stock_status.short_description = 'Status'


@admin.register(StockTransaction)
class StockTransactionAdmin(admin.ModelAdmin):
    list_display = [
        'transaction_id', 'product_code', 'warehouse_code', 'transaction_type',
        'quantity', 'unit_cost', 'total_cost', 'reference_number', 'transaction_date'
    ]
    list_filter = [
        'transaction_type', 'warehouse', 'transaction_date', 'reference_type'
    ]
    search_fields = [
        'product__code', 'product__name', 'reference_number', 'description'
    ]
    ordering = ['-transaction_date', '-created_at']

    readonly_fields = ['transaction_id', 'total_cost', 'balance_quantity', 'balance_value', 'created_at']

    def product_code(self, obj):
        return obj.product.code
    product_code.short_description = 'Product'
    product_code.admin_order_field = 'product__code'

    def warehouse_code(self, obj):
        return obj.warehouse.code
    warehouse_code.short_description = 'Warehouse'
    warehouse_code.admin_order_field = 'warehouse__code'


@admin.register(StockAdjustment)
class StockAdjustmentAdmin(admin.ModelAdmin):
    list_display = [
        'adjustment_number', 'warehouse', 'reason', 'status',
        'total_items', 'total_adjustment_value', 'adjustment_date'
    ]
    list_filter = ['status', 'reason', 'warehouse', 'adjustment_date']
    search_fields = ['adjustment_number', 'description']
    ordering = ['-adjustment_date', '-created_at']

    readonly_fields = [
        'adjustment_number', 'total_items', 'total_adjustment_value',
        'created_at', 'updated_at', 'posted_at'
    ]

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(StockTransfer)
class StockTransferAdmin(admin.ModelAdmin):
    list_display = [
        'transfer_number', 'from_warehouse', 'to_warehouse', 'status',
        'total_items', 'total_value', 'transfer_date'
    ]
    list_filter = ['status', 'from_warehouse', 'to_warehouse', 'transfer_date']
    search_fields = ['transfer_number', 'description']
    ordering = ['-transfer_date', '-created_at']

    readonly_fields = [
        'transfer_number', 'total_items', 'total_quantity', 'total_value',
        'created_at', 'updated_at', 'shipped_at', 'received_at'
    ]

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(StockValuation)
class StockValuationAdmin(admin.ModelAdmin):
    list_display = [
        'valuation_date', 'warehouse', 'method', 'status',
        'total_items', 'total_value', 'created_at'
    ]
    list_filter = ['status', 'method', 'warehouse', 'valuation_date']
    search_fields = ['warehouse__code', 'warehouse__name']
    ordering = ['-valuation_date', '-created_at']

    readonly_fields = [
        'total_items', 'total_quantity', 'total_value',
        'created_at', 'finalized_at'
    ]

    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

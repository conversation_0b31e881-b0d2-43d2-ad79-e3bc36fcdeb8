# Master Product Models - ERP Best Practices
# Central product definition - source of truth for all modules

from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
import uuid

class ProductCategory(models.Model):
    """Product categories for organization"""
    category_id = models.AutoField(primary_key=True)
    code = models.CharField(max_length=20, unique=True, help_text="Category code (e.g., ELEC, FURN)")
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    parent_category = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='subcategories')
    is_active = models.BooleanField(default=True)
    
    # Audit trail
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name='product_categories_created')
    
    class Meta:
        db_table = 'product_categories'
        verbose_name_plural = 'Product Categories'
        ordering = ['name']
    
    def __str__(self):
        return f"{self.code} - {self.name}"


class Product(models.Model):
    """
    Master Product Table - ERP Best Practice
    
    This is the single source of truth for all products in the system.
    Contains ONLY basic product information - no pricing, no inventory.
    
    Other modules reference this:
    - Inventory: tracks stock levels
    - Pricing: manages sale/cost prices  
    - Purchase: for buying
    - Sales: for selling (via pricing)
    """
    
    # Product Types
    PRODUCT = 'product'
    SERVICE = 'service'
    BUNDLE = 'bundle'
    TYPE_CHOICES = [
        (PRODUCT, 'Product'),
        (SERVICE, 'Service'),
        (BUNDLE, 'Bundle'),
    ]
    
    # Primary Key
    product_id = models.AutoField(primary_key=True)
    
    # Basic Information
    code = models.CharField(max_length=50, unique=True, help_text="Unique product identifier/SKU")
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    type = models.CharField(max_length=10, choices=TYPE_CHOICES, default=PRODUCT)
    category = models.ForeignKey(ProductCategory, on_delete=models.SET_NULL, null=True, blank=True, related_name='products')
    
    # Physical Properties
    uom = models.CharField(max_length=20, default='each', help_text="Unit of Measure (each, kg, liter, etc.)")
    weight = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, help_text="Weight in kg")
    dimensions = models.CharField(max_length=100, blank=True, help_text="L x W x H in cm")
    
    # Tracking Options
    track_inventory = models.BooleanField(default=True, help_text="Track stock levels for this product")
    track_serial_numbers = models.BooleanField(default=False, help_text="Track individual serial numbers")
    track_batches = models.BooleanField(default=False, help_text="Track batch/lot numbers")
    
    # Status
    is_active = models.BooleanField(default=True)
    is_purchasable = models.BooleanField(default=True, help_text="Can be purchased from vendors")
    is_sellable = models.BooleanField(default=True, help_text="Can be sold to customers (requires pricing)")
    
    # Audit trail
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name='products_created')
    
    class Meta:
        db_table = 'products'
        ordering = ['code', 'name']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['name']),
            models.Index(fields=['type']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    @property
    def full_name(self):
        """Full product name with code"""
        return f"{self.code} - {self.name}"
    
    def can_be_sold(self):
        """Check if product can be sold (active, sellable, and has pricing)"""
        if not (self.is_active and self.is_sellable):
            return False
        
        # Check if product has pricing defined
        try:
            from Pricing.models import Product as PricingProduct
            return PricingProduct.objects.filter(product=self, is_active=True).exists()
        except:
            return False
    
    def can_be_purchased(self):
        """Check if product can be purchased"""
        return self.is_active and self.is_purchasable
    
    def has_inventory(self):
        """Check if product has inventory tracking"""
        return self.track_inventory and self.type == self.PRODUCT
    
    def get_current_stock(self, warehouse_id=None):
        """Get current stock level"""
        if not self.has_inventory():
            return None
        
        try:
            from inventory.models import Inventory
            if warehouse_id:
                inventory = Inventory.objects.filter(product=self, warehouse_id=warehouse_id).first()
                return inventory.quantity_on_hand if inventory else 0
            else:
                # Total across all warehouses
                total = Inventory.objects.filter(product=self).aggregate(
                    total=models.Sum('quantity_on_hand')
                )['total']
                return total or 0
        except:
            return 0
    
    def get_available_stock(self, warehouse_id=None):
        """Get available stock (on_hand - reserved)"""
        if not self.has_inventory():
            return None
        
        try:
            from inventory.models import Inventory
            if warehouse_id:
                inventory = Inventory.objects.filter(product=self, warehouse_id=warehouse_id).first()
                return inventory.available_quantity if inventory else 0
            else:
                # Total available across all warehouses
                inventories = Inventory.objects.filter(product=self)
                total_available = sum(inv.available_quantity for inv in inventories)
                return total_available
        except:
            return 0


class ProductAttribute(models.Model):
    """Custom attributes for products (color, size, etc.)"""
    attribute_id = models.AutoField(primary_key=True)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='attributes')
    name = models.CharField(max_length=50, help_text="Attribute name (e.g., Color, Size)")
    value = models.CharField(max_length=100, help_text="Attribute value (e.g., Red, Large)")
    
    class Meta:
        db_table = 'product_attributes'
        unique_together = ['product', 'name']
    
    def __str__(self):
        return f"{self.product.code} - {self.name}: {self.value}"


class ProductImage(models.Model):
    """Product images"""
    image_id = models.AutoField(primary_key=True)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='products/', help_text="Product image")
    alt_text = models.CharField(max_length=200, blank=True)
    is_primary = models.BooleanField(default=False, help_text="Primary product image")
    sort_order = models.PositiveIntegerField(default=0)
    
    class Meta:
        db_table = 'product_images'
        ordering = ['sort_order', 'image_id']
    
    def __str__(self):
        return f"{self.product.code} - Image {self.image_id}"


class ProductBundle(models.Model):
    """Bundle products - products that contain other products"""
    bundle_id = models.AutoField(primary_key=True)
    bundle_product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='bundle_items')
    component_product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='used_in_bundles')
    quantity = models.DecimalField(max_digits=15, decimal_places=4, validators=[MinValueValidator(0.0001)])
    
    class Meta:
        db_table = 'product_bundles'
        unique_together = ['bundle_product', 'component_product']
    
    def __str__(self):
        return f"{self.bundle_product.code} contains {self.quantity} x {self.component_product.code}"


class ProductSupplier(models.Model):
    """Preferred suppliers for products"""
    supplier_id = models.AutoField(primary_key=True)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='suppliers')
    vendor = models.ForeignKey('contacts.Contact', on_delete=models.CASCADE, related_name='supplied_products')
    vendor_product_code = models.CharField(max_length=100, blank=True, help_text="Vendor's product code")
    is_preferred = models.BooleanField(default=False)
    lead_time_days = models.PositiveIntegerField(default=0, help_text="Lead time in days")
    minimum_order_qty = models.DecimalField(max_digits=15, decimal_places=4, default=1)
    
    class Meta:
        db_table = 'product_suppliers'
        unique_together = ['product', 'vendor']
    
    def __str__(self):
        return f"{self.product.code} from {self.vendor.name}"

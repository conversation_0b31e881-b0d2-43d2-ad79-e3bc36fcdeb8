from django.contrib import admin
from .models import ProductCategory, Product, ProductAttribute, ProductImage, ProductBundle, ProductSupplier


@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'parent_category', 'is_active', 'created_at']
    list_filter = ['is_active', 'parent_category']
    search_fields = ['code', 'name']
    ordering = ['code']


class ProductAttributeInline(admin.TabularInline):
    model = ProductAttribute
    extra = 0


class ProductImageInline(admin.TabularInline):
    model = ProductImage
    extra = 0


class ProductBundleInline(admin.TabularInline):
    model = ProductBundle
    fk_name = 'bundle_product'
    extra = 0


class ProductSupplierInline(admin.TabularInline):
    model = ProductSupplier
    extra = 0


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'type', 'category', 'track_inventory', 'is_active', 'created_at']
    list_filter = ['type', 'category', 'track_inventory', 'is_active', 'is_purchasable', 'is_sellable']
    search_fields = ['code', 'name', 'description']
    ordering = ['code']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('code', 'name', 'description', 'type', 'category')
        }),
        ('Physical Properties', {
            'fields': ('uom', 'weight', 'dimensions'),
            'classes': ('collapse',)
        }),
        ('Tracking Options', {
            'fields': ('track_inventory', 'track_serial_numbers', 'track_batches')
        }),
        ('Status', {
            'fields': ('is_active', 'is_purchasable', 'is_sellable')
        }),
    )
    
    inlines = [ProductAttributeInline, ProductImageInline, ProductBundleInline, ProductSupplierInline]
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

"""
Inventory Signals
Automatic stock level updates and integrations
"""

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from .models import StockTransaction, StockLevel


@receiver(post_save, sender=StockTransaction)
def update_stock_level_on_transaction(sender, instance, created, **kwargs):
    """
    Automatically update stock levels when transactions are created
    This ensures real-time inventory tracking
    """
    if created:
        # Get or create stock level record
        stock_level, level_created = StockLevel.objects.get_or_create(
            product=instance.product,
            warehouse=instance.warehouse,
            defaults={
                'quantity_on_hand': 0,
                'average_cost': instance.unit_cost,
                'last_cost': instance.unit_cost,
                'standard_cost': instance.unit_cost,
            }
        )
        
        # Update stock level based on transaction type
        if instance.transaction_type in ['RECEIPT', 'TRANSFER_IN', 'RETURN']:
            # Inbound transactions - increase stock
            old_quantity = stock_level.quantity_on_hand
            old_cost = stock_level.average_cost
            new_quantity = old_quantity + abs(instance.quantity)
            
            # Update weighted average cost
            if old_quantity > 0 and instance.unit_cost > 0:
                total_cost = (old_quantity * old_cost) + (abs(instance.quantity) * instance.unit_cost)
                stock_level.average_cost = total_cost / new_quantity
            elif instance.unit_cost > 0:
                stock_level.average_cost = instance.unit_cost
            
            stock_level.quantity_on_hand = new_quantity
            stock_level.last_cost = instance.unit_cost
            
        elif instance.transaction_type in ['ISSUE', 'TRANSFER_OUT', 'DAMAGE']:
            # Outbound transactions - decrease stock
            stock_level.quantity_on_hand -= abs(instance.quantity)
            # Ensure stock doesn't go negative unless allowed
            if not stock_level.warehouse.allow_negative_stock:
                stock_level.quantity_on_hand = max(0, stock_level.quantity_on_hand)
            
        elif instance.transaction_type in ['ADJUSTMENT', 'CYCLE_COUNT']:
            # Adjustment transactions - direct quantity change
            stock_level.quantity_on_hand += instance.quantity
            if not stock_level.warehouse.allow_negative_stock:
                stock_level.quantity_on_hand = max(0, stock_level.quantity_on_hand)
        
        # Update timestamps
        stock_level.last_transaction_date = instance.transaction_date
        stock_level.save()
        
        # Update transaction balance fields
        instance.balance_quantity = stock_level.quantity_on_hand
        instance.balance_value = stock_level.stock_value_average
        StockTransaction.objects.filter(pk=instance.pk).update(
            balance_quantity=instance.balance_quantity,
            balance_value=instance.balance_value
        )

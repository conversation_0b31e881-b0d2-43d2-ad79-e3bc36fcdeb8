"""
Inventory Pagination Classes
Following our established patterns
"""

from rest_framework.pagination import PageNumberPagination


class InventoryPagination(PageNumberPagination):
    """Standard pagination for inventory data"""
    page_size = 25
    page_size_query_param = 'page_size'
    max_page_size = 100


class TransactionPagination(PageNumberPagination):
    """Pagination for transaction history (larger page size)"""
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 200


class NoPagination(PageNumberPagination):
    """No pagination for dropdown/select data"""
    page_size = None

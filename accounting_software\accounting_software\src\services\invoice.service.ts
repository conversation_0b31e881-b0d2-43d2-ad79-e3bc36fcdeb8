import api from './api';

// Invoice Types
export interface InvoiceCustomer {
  id: number;
  display_name: string;
  email?: string;
  phone?: string;
  billing_address?: string;
  payment_terms?: string;
}

export interface InvoiceItem {
  id?: number;
  product?: number;
  product_name?: string;
  description: string;
  quantity: number;
  unit_price: number;
  line_total: number;
  tax_rate?: number;
  tax_amount?: number;
}

export interface Invoice {
  id?: number;
  invoice_number?: string;
  customer: number;
  customer_details?: InvoiceCustomer;
  invoice_date: string;
  due_date: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  items: InvoiceItem[];
  subtotal: number;
  tax_total: number;
  total_amount: number;
  notes?: string;
  terms?: string;
  created_at?: string;
  updated_at?: string;
}

export interface InvoiceFilters {
  status?: string;
  customer?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface InvoiceStats {
  total_invoices: number;
  total_revenue: number;
  outstanding_amount: number;
  overdue_count: number;
  draft_count: number;
  paid_count: number;
}

class InvoiceService {
  private baseUrl = '/sales';

  // Get all invoices with optional filters
  async getInvoices(filters?: InvoiceFilters): Promise<{ results: Invoice[]; count: number }> {
    const params = new URLSearchParams();
    if (filters?.status) params.append('status', filters.status);
    if (filters?.customer) params.append('customer', filters.customer.toString());
    if (filters?.date_from) params.append('date_from', filters.date_from);
    if (filters?.date_to) params.append('date_to', filters.date_to);
    if (filters?.search) params.append('search', filters.search);

    const response = await api.get(`${this.baseUrl}/invoices/?${params}`);
    return response.data;
  }

  // Get single invoice by ID
  async getInvoice(id: number): Promise<Invoice> {
    const response = await api.get(`${this.baseUrl}/invoices/${id}/`);
    return response.data;
  }

  // Create new invoice
  async createInvoice(invoiceData: Omit<Invoice, 'id' | 'invoice_number' | 'created_at' | 'updated_at'>): Promise<Invoice> {
    const response = await api.post(`${this.baseUrl}/invoices/`, invoiceData);
    return response.data;
  }

  // Update existing invoice
  async updateInvoice(id: number, invoiceData: Partial<Invoice>): Promise<Invoice> {
    const response = await api.patch(`${this.baseUrl}/invoices/${id}/`, invoiceData);
    return response.data;
  }

  // Delete invoice
  async deleteInvoice(id: number): Promise<void> {
    await api.delete(`${this.baseUrl}/invoices/${id}/`);
  }

  // Get invoice statistics
  async getInvoiceStats(): Promise<InvoiceStats> {
    const response = await api.get(`${this.baseUrl}/invoices/stats/`);
    return response.data;
  }

  // Send invoice to customer
  async sendInvoice(id: number, emailData?: { to?: string; subject?: string; message?: string }): Promise<void> {
    await api.post(`${this.baseUrl}/invoices/${id}/send/`, emailData || {});
  }

  // Mark invoice as paid
  async markAsPaid(id: number, paymentData?: { amount?: number; payment_date?: string; notes?: string }): Promise<Invoice> {
    const response = await api.post(`${this.baseUrl}/invoices/${id}/mark_paid/`, paymentData || {});
    return response.data;
  }

  // Get customers for invoice creation
  async getCustomers(): Promise<InvoiceCustomer[]> {
    const response = await api.get(`${this.baseUrl}/customers/`);
    return response.data.results || response.data;
  }

  // Get products for invoice line items
  async getProducts(): Promise<any[]> {
    const response = await api.get(`${this.baseUrl}/products/`);
    return response.data.results || response.data;
  }

  // Helper method to calculate invoice totals
  calculateTotals(items: InvoiceItem[]): { subtotal: number; tax_total: number; total_amount: number } {
    const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0);
    const tax_total = items.reduce((sum, item) => sum + (item.tax_amount || 0), 0);
    const total_amount = subtotal + tax_total;

    return { subtotal, tax_total, total_amount };
  }

  // Helper method to generate due date based on payment terms
  calculateDueDate(invoiceDate: string, paymentTerms: string = 'net_30'): string {
    const date = new Date(invoiceDate);
    const daysToAdd = this.getPaymentTermsDays(paymentTerms);
    date.setDate(date.getDate() + daysToAdd);
    return date.toISOString().split('T')[0];
  }

  private getPaymentTermsDays(paymentTerms: string): number {
    const termMap: { [key: string]: number } = {
      'net_15': 15,
      'net_30': 30,
      'net_45': 45,
      'net_60': 60,
      'due_on_receipt': 0,
      'due_end_of_month': 30,
    };
    return termMap[paymentTerms] || 30;
  }
}

export const invoiceService = new InvoiceService();
export default invoiceService; 
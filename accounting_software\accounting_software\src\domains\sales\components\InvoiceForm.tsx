import React from 'react';
import { Typography, Box } from '@mui/material';

interface InvoiceFormProps {
  onSuccess?: () => void;
  invoice?: any;
  fullPage?: boolean;
  onSubmit?: (values: any, action: string) => void;
}

const InvoiceForm: React.FC<InvoiceFormProps> = ({ onSuccess }) => {
  return (
    <Box sx={{ p: 3, textAlign: 'center' }}>
      <Typography variant="h6" gutterBottom>
        Invoice Form
      </Typography>
      <Typography variant="body2" color="text.secondary">
        Invoice form is temporarily disabled due to type compatibility issues.
        Please use the CreateInvoicePage for invoice creation.
      </Typography>
    </Box>
  );
};

export default InvoiceForm; 
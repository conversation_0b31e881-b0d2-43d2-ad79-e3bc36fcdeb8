from django.shortcuts import render
from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Sum, Count, F, Value, DecimalField
from django.db import models
from django.utils import timezone
from datetime import datetime, timedelta
from django.db.models.functions import Coalesce

from .models import (
    ProductCategory, Product, Payment, PaymentTerm
)
from contacts.models import Contact
from .serializers import (
    # CustomerSerializer moved to contacts app
    ProductCategorySerializer, ProductSerializer,
    PaymentSerializer, PaymentTermSerializer,
    ProductPricingSerializer
)
from gl.models import Account
from inventory.models import Inventory


# DEPRECATED: CustomerViewSet moved to contacts app
# Use /api/contacts/customers/ instead of /api/sales/customers/
# This class is commented out to avoid conflicts with the proper implementation in contacts.views

"""
DEPRECATED CustomerViewSet - moved to contacts app

class CustomerViewSet(viewsets.ModelViewSet):
    ViewSet for managing customers - MOVED TO CONTACTS APP
    queryset = Contact.objects.all()
    serializer_class = CustomerSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'customer_type', 'taxable']
    search_fields = ['display_name', 'company_name', 'email', 'phone', 'gstin']
    ordering_fields = ['display_name', 'created_at', 'current_balance']
    ordering = ['display_name']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by balance
        min_balance = self.request.query_params.get('min_balance')
        max_balance = self.request.query_params.get('max_balance')

        if min_balance is not None:
            queryset = queryset.filter(current_balance__gte=min_balance)
        if max_balance is not None:
            queryset = queryset.filter(current_balance__lte=max_balance)

        return queryset

    @action(detail=False, methods=['get'])
    def stats(self, request):
        Get customer statistics
        total_customers = self.get_queryset().count()
        active_customers = self.get_queryset().filter(status='active').count()
        inactive_customers = self.get_queryset().filter(status='inactive').count()

        # Calculate total receivables
        total_receivables = self.get_queryset().aggregate(
            total=Sum('current_balance')
        )['total'] or 0

        # Get recent customers (last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        new_customers = self.get_queryset().filter(
            created_at__gte=thirty_days_ago
        ).count()

        return Response({
            'total_customers': total_customers,
            'active_customers': active_customers,
            'inactive_customers': inactive_customers,
            'total_receivables': float(total_receivables),
            'new_customers_30_days': new_customers,
        })

    # invoices method removed - will be replaced with new invoice system

    @action(detail=True, methods=['get'])
    def payments(self, request, pk=None):
        Get customer's payments
        customer = self.get_object()
        payments = customer.payments.all().order_by('-payment_date')
        serializer = PaymentSerializer(payments, many=True)
        return Response(serializer.data)
"""


class ProductCategoryViewSet(viewsets.ModelViewSet):
    """Enhanced ViewSet for managing product categories"""
    queryset = ProductCategory.objects.all()
    serializer_class = ProductCategorySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['division_type', 'is_active', 'parent_category', 'level']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'code', 'sort_order', 'created_at']
    ordering = ['sort_order', 'name']

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by division type
        division_type = self.request.query_params.get('division_type')
        if division_type:
            queryset = queryset.filter(division_type=division_type)
        
        # Filter by hierarchy level
        level = self.request.query_params.get('level')
        if level:
            queryset = queryset.filter(level=level)
        
        # Filter top-level categories only
        top_level_only = self.request.query_params.get('top_level_only')
        if top_level_only == 'true':
            queryset = queryset.filter(parent_category__isnull=True)
        
        return queryset

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get category statistics"""
        total_categories = self.get_queryset().count()
        active_categories = self.get_queryset().filter(is_active=True).count()
        
        # Count by division type
        division_stats = {}
        for division_type, _ in ProductCategory.DIVISION_TYPE_CHOICES:
            count = self.get_queryset().filter(division_type=division_type, is_active=True).count()
            division_stats[division_type] = count
        
        # Count by level
        level_stats = {}
        for level in [1, 2, 3]:
            count = self.get_queryset().filter(level=level, is_active=True).count()
            level_stats[f'level_{level}'] = count
        
        return Response({
            'total_categories': total_categories,
            'active_categories': active_categories,
            'division_stats': division_stats,
            'level_stats': level_stats,
        })

    @action(detail=False, methods=['get'])
    def hierarchy(self, request):
        """Get categories in hierarchical structure"""
        # Get top-level categories
        top_level = self.get_queryset().filter(parent_category__isnull=True, is_active=True)
        serializer = self.get_serializer(top_level, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def subcategories(self, request, pk=None):
        """Get subcategories of a specific category"""
        category = self.get_object()
        subcategories = category.subcategories.filter(is_active=True).order_by('sort_order', 'name')
        serializer = self.get_serializer(subcategories, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def products(self, request, pk=None):
        """Get products in this category"""
        category = self.get_object()
        products = category.products.filter(status='active').order_by('name')
        # Import ProductSerializer here to avoid circular imports
        from .serializers import ProductSerializer
        serializer = ProductSerializer(products, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_division(self, request):
        """Get categories grouped by division type"""
        division_type = request.query_params.get('division_type')
        if not division_type:
            return Response({'error': 'division_type parameter is required'}, status=400)
        
        categories = self.get_queryset().filter(
            division_type=division_type, 
            is_active=True
        ).order_by('sort_order', 'name')
        
        serializer = self.get_serializer(categories, many=True)
        return Response(serializer.data)


class ProductViewSet(viewsets.ModelViewSet):
    """ViewSet for managing products and services"""
    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'product_type', 'category', 'taxable', 'track_inventory']
    search_fields = ['name', 'sku', 'description']
    ordering_fields = ['name', 'unit_price', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        queryset = super().get_queryset()

        # NOTE: Inventory annotation removed since products moved to Pricing module
        # Inventory data should be fetched from /api/inventory/ endpoints
        # This endpoint is deprecated - use /api/pricing/products/ instead

        # Filter by price range
        min_price = self.request.query_params.get('min_price')
        max_price = self.request.query_params.get('max_price')

        if min_price is not None:
            queryset = queryset.filter(unit_price__gte=min_price)
        if max_price is not None:
            queryset = queryset.filter(unit_price__lte=max_price)

        # Low stock filtering removed - use inventory endpoints instead

        return queryset

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get product statistics - DEPRECATED

        This endpoint is deprecated. Use /api/pricing/products/stats/ instead.
        Inventory statistics should be fetched from /api/inventory/stats/
        """
        total_products = self.get_queryset().filter(product_type='product').count()
        total_services = self.get_queryset().filter(product_type='service').count()

        # Inventory value calculation removed - use inventory endpoints instead
        inventory_value = 0

        # Low stock count removed - use inventory endpoints instead
        low_stock_count = 0

        return Response({
            'total_products': total_products,
            'total_services': total_services,
            'inventory_value': float(inventory_value),
            'low_stock_count': low_stock_count,
            'deprecated': True,
            'message': 'This endpoint is deprecated. Use /api/pricing/products/stats/ and /api/inventory/stats/ instead.'
        })

    @action(detail=False, methods=['get'])
    def low_stock(self, request):
        """Get products with low stock - DEPRECATED

        This endpoint is deprecated. Use /api/inventory/low-stock/ instead.
        """
        return Response({
            'deprecated': True,
            'message': 'This endpoint is deprecated. Use /api/inventory/low-stock/ instead.',
            'data': []
        })

    @action(detail=False, methods=['get'])
    def gl_accounts(self, request):
        """Get available GL accounts for product setup"""
        account_type = request.query_params.get('type', 'all')
        
        if account_type == 'revenue':
            accounts = Account.objects.filter(
                account_type__type='REVENUE',
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        elif account_type == 'expense':
            accounts = Account.objects.filter(
                account_type__type='EXPENSE',
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        elif account_type == 'asset':
            accounts = Account.objects.filter(
                account_type__type='ASSET',
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        else:
            # Return all accounts suitable for products
            accounts = Account.objects.filter(
                account_type__type__in=['REVENUE', 'EXPENSE', 'ASSET'],
                is_active=True,
                is_header_account=False
            ).order_by('account_type__type', 'account_number')
        
        # Simple serialization for dropdown
        account_data = [
            {
                'id': acc.id,
                'account_number': acc.account_number,
                'account_name': acc.account_name,
                'account_type': acc.account_type.type,
                'display_name': f"{acc.account_number} - {acc.account_name}"
            }
            for acc in accounts
        ]
        
        return Response(account_data)

    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        """Get all active products for dropdown (optimized for search)"""
        queryset = self.get_queryset().filter(status='active').order_by('name')
        
        # Apply search if provided
        search = request.query_params.get('search', '')
        if search:
            queryset = queryset.filter(
                models.Q(name__icontains=search) |
                models.Q(sku__icontains=search) |
                models.Q(description__icontains=search)
            )
        
        # Limit results for performance
        limit = int(request.query_params.get('limit', 50))
        queryset = queryset[:limit]
        
        # Simple serialization for dropdown
        product_data = [
            {
                'id': product.id,
                'name': product.name,
                'sku': product.sku,
                'unit_price': str(product.unit_price),
                'cost_price': str(product.cost_price),
                'display_name': f"{product.name} ({product.sku})" if product.sku else product.name,
                'track_inventory': product.track_inventory,
                'quantity_on_hand': product.quantity_on_hand,
            }
            for product in queryset
        ]
        
        return Response(product_data)


# InvoiceViewSet removed - will be replaced with new invoice system


class PaymentViewSet(viewsets.ModelViewSet):
    """ViewSet for managing payments"""
    queryset = Payment.objects.all()
    serializer_class = PaymentSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['payment_method', 'customer']
    search_fields = ['reference_number', 'customer__display_name']
    ordering_fields = ['payment_date', 'amount', 'created_at']
    ordering = ['-payment_date']

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(payment_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(payment_date__lte=end_date)
        
        return queryset.select_related('customer')


# EstimateViewSet removed - will be replaced with new system


class PaymentTermViewSet(viewsets.ModelViewSet):
    """ViewSet for managing payment terms"""
    queryset = PaymentTerm.objects.filter(is_active=True)
    serializer_class = PaymentTermSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'days', 'created_at']
    ordering = ['days', 'name']

    @action(detail=False, methods=['get'])
    def default(self, request):
        """Get the default payment term"""
        try:
            default_term = PaymentTerm.objects.get(is_default=True, is_active=True)
            serializer = self.get_serializer(default_term)
            return Response(serializer.data)
        except PaymentTerm.DoesNotExist:
            return Response({'error': 'No default payment term found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def set_default(self, request, pk=None):
        """Set this payment term as default"""
        payment_term = self.get_object()
        
        # Remove default from all other terms
        PaymentTerm.objects.filter(is_default=True).update(is_default=False)
        
        # Set this term as default
        payment_term.is_default = True
        payment_term.save()
        
        serializer = self.get_serializer(payment_term)
        return Response(serializer.data)


# SalesOrderViewSet and DeliveryNoteViewSet removed - will be replaced with new system


# Enhanced Product ViewSet with Sales Price Authority
class ProductPricingViewSet(viewsets.ModelViewSet):
    """Special ViewSet for Sales Department to manage product pricing"""
    queryset = Product.objects.filter(status='active')
    serializer_class = ProductPricingSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'status', 'track_inventory']
    search_fields = ['name', 'sku', 'description']
    ordering_fields = ['name', 'sku', 'sale_price', 'cost_price', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by category
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category_id=category)

        # Filter by price range
        min_price = self.request.query_params.get('min_price')
        max_price = self.request.query_params.get('max_price')

        if min_price:
            queryset = queryset.filter(sale_price__gte=min_price)
        if max_price:
            queryset = queryset.filter(sale_price__lte=max_price)

        return queryset.select_related('category')

    @action(detail=False, methods=['get'])
    def pricing_stats(self, request):
        """Get pricing statistics for products"""
        queryset = self.get_queryset()

        total_products = queryset.count()
        avg_sale_price = queryset.aggregate(avg_price=models.Avg('sale_price'))['avg_price'] or 0
        avg_cost_price = queryset.aggregate(avg_cost=models.Avg('cost_price'))['avg_cost'] or 0

        # Price ranges
        price_ranges = {
            'under_10': queryset.filter(sale_price__lt=10).count(),
            '10_to_50': queryset.filter(sale_price__gte=10, sale_price__lt=50).count(),
            '50_to_100': queryset.filter(sale_price__gte=50, sale_price__lt=100).count(),
            'over_100': queryset.filter(sale_price__gte=100).count(),
        }

        return Response({
            'total_products': total_products,
            'avg_sale_price': float(avg_sale_price),
            'avg_cost_price': float(avg_cost_price),
            'price_ranges': price_ranges,
        })

    @action(detail=False, methods=['get'])
    def margin_analysis(self, request):
        """Analyze profit margins across products"""
        queryset = self.get_queryset().exclude(cost_price=0)

        # Calculate margin ranges
        margin_ranges = {
            'below_20': queryset.filter(unit_price__lt=F('cost_price') * 1.2).count(),
            '20_to_50': queryset.filter(
                unit_price__gte=F('cost_price') * 1.2,
                unit_price__lt=F('cost_price') * 1.5
            ).count(),
            'above_50': queryset.filter(unit_price__gte=F('cost_price') * 1.5).count(),
        }

        return Response(margin_ranges)

/**
 * Enterprise Inventory Dashboard
 * Following our established patterns with comprehensive overview
 */

import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Table, Tag, Button, Space, Alert, Spin } from 'antd';
import { 
  ShopOutlined, 
  InboxOutlined, 
  ExclamationTriangleOutlined, 
  StopOutlined,
  SwapOutlined,
  EditOutlined,
  DollarOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { inventoryService, InventorySummary, WarehouseSummary, StockLevel } from '../services/inventory.service';

const InventoryDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [inventorySummary, setInventorySummary] = useState<InventorySummary | null>(null);
  const [warehouseSummary, setWarehouseSummary] = useState<WarehouseSummary[]>([]);
  const [lowStockItems, setLowStockItems] = useState<StockLevel[]>([]);
  const [outOfStockItems, setOutOfStockItems] = useState<StockLevel[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [summary, warehouses, lowStock, outOfStock] = await Promise.all([
        inventoryService.getInventorySummary(),
        inventoryService.getWarehouseSummary(),
        inventoryService.getLowStockItems(),
        inventoryService.getOutOfStockItems()
      ]);

      setInventorySummary(summary);
      setWarehouseSummary(warehouses);
      setLowStockItems(lowStock.slice(0, 10)); // Show top 10
      setOutOfStockItems(outOfStock.slice(0, 10)); // Show top 10

    } catch (err) {
      console.error('❌ Error loading dashboard data:', err);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: string | number) => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(num);
  };

  const formatNumber = (value: string | number) => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-US').format(num);
  };

  const warehouseColumns = [
    {
      title: 'Warehouse',
      dataIndex: 'warehouse_code',
      key: 'warehouse_code',
      render: (code: string, record: WarehouseSummary) => (
        <div>
          <div className="font-medium">{code}</div>
          <div className="text-sm text-gray-500">{record.warehouse_name}</div>
        </div>
      ),
    },
    {
      title: 'Products',
      dataIndex: 'total_products',
      key: 'total_products',
      render: (value: number) => formatNumber(value),
    },
    {
      title: 'Total Quantity',
      dataIndex: 'total_quantity',
      key: 'total_quantity',
      render: (value: string) => formatNumber(value),
    },
    {
      title: 'Total Value',
      dataIndex: 'total_value',
      key: 'total_value',
      render: (value: string) => formatCurrency(value),
    },
    {
      title: 'Alerts',
      key: 'alerts',
      render: (_: any, record: WarehouseSummary) => (
        <Space>
          {record.low_stock_items > 0 && (
            <Tag color="orange" icon={<ExclamationTriangleOutlined />}>
              {record.low_stock_items} Low
            </Tag>
          )}
          {record.out_of_stock_items > 0 && (
            <Tag color="red" icon={<StopOutlined />}>
              {record.out_of_stock_items} Out
            </Tag>
          )}
        </Space>
      ),
    },
  ];

  const stockAlertColumns = [
    {
      title: 'Product',
      dataIndex: 'product_code',
      key: 'product_code',
      render: (code: string, record: StockLevel) => (
        <div>
          <div className="font-medium">{code}</div>
          <div className="text-sm text-gray-500">{record.product_name}</div>
        </div>
      ),
    },
    {
      title: 'Warehouse',
      dataIndex: 'warehouse_code',
      key: 'warehouse_code',
    },
    {
      title: 'On Hand',
      dataIndex: 'quantity_on_hand',
      key: 'quantity_on_hand',
      render: (value: string) => formatNumber(value),
    },
    {
      title: 'Available',
      dataIndex: 'available_quantity',
      key: 'available_quantity',
      render: (value: string) => formatNumber(value),
    },
    {
      title: 'Reorder Point',
      dataIndex: 'reorder_point',
      key: 'reorder_point',
      render: (value: string) => formatNumber(value),
    },
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Error"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={loadDashboardData}>
            Retry
          </Button>
        }
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Inventory Dashboard</h1>
          <p className="text-gray-600">Enterprise inventory management overview</p>
        </div>
        <Space>
          <Button 
            type="primary" 
            icon={<EditOutlined />}
            onClick={() => navigate('/dashboard/inventory/adjustments/new')}
          >
            New Adjustment
          </Button>
          <Button 
            icon={<SwapOutlined />}
            onClick={() => navigate('/dashboard/inventory/transfers/new')}
          >
            New Transfer
          </Button>
        </Space>
      </div>

      {/* Summary Statistics */}
      {inventorySummary && (
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Warehouses"
                value={inventorySummary.total_warehouses}
                prefix={<ShopOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Products"
                value={inventorySummary.total_products}
                prefix={<InboxOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Quantity"
                value={formatNumber(inventorySummary.total_quantity)}
                prefix={<BarChartOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Value"
                value={formatCurrency(inventorySummary.total_value)}
                prefix={<DollarOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Alert Statistics */}
      {inventorySummary && (inventorySummary.low_stock_items > 0 || inventorySummary.out_of_stock_items > 0) && (
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12}>
            <Card>
              <Statistic
                title="Low Stock Items"
                value={inventorySummary.low_stock_items}
                prefix={<ExclamationTriangleOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
              <Button 
                type="link" 
                size="small"
                onClick={() => navigate('/dashboard/inventory/stock-levels?filter=low_stock')}
              >
                View Details
              </Button>
            </Card>
          </Col>
          <Col xs={24} sm={12}>
            <Card>
              <Statistic
                title="Out of Stock Items"
                value={inventorySummary.out_of_stock_items}
                prefix={<StopOutlined />}
                valueStyle={{ color: '#ff4d4f' }}
              />
              <Button 
                type="link" 
                size="small"
                onClick={() => navigate('/dashboard/inventory/stock-levels?filter=out_of_stock')}
              >
                View Details
              </Button>
            </Card>
          </Col>
        </Row>
      )}

      {/* Warehouse Summary */}
      <Card 
        title="Warehouse Overview" 
        extra={
          <Button 
            type="link" 
            onClick={() => navigate('/dashboard/inventory/warehouses')}
          >
            View All
          </Button>
        }
      >
        <Table
          dataSource={warehouseSummary}
          columns={warehouseColumns}
          rowKey="warehouse_id"
          pagination={false}
          size="small"
        />
      </Card>

      {/* Stock Alerts */}
      <Row gutter={[16, 16]}>
        {lowStockItems.length > 0 && (
          <Col xs={24} lg={12}>
            <Card 
              title="Low Stock Alert" 
              extra={
                <Button 
                  type="link" 
                  onClick={() => navigate('/dashboard/inventory/stock-levels?filter=low_stock')}
                >
                  View All
                </Button>
              }
            >
              <Table
                dataSource={lowStockItems}
                columns={stockAlertColumns}
                rowKey="stock_level_id"
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
        )}

        {outOfStockItems.length > 0 && (
          <Col xs={24} lg={12}>
            <Card 
              title="Out of Stock Alert" 
              extra={
                <Button 
                  type="link" 
                  onClick={() => navigate('/dashboard/inventory/stock-levels?filter=out_of_stock')}
                >
                  View All
                </Button>
              }
            >
              <Table
                dataSource={outOfStockItems}
                columns={stockAlertColumns}
                rowKey="stock_level_id"
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
        )}
      </Row>

      {/* Quick Actions */}
      <Card title="Quick Actions">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8}>
            <Button 
              block 
              size="large"
              onClick={() => navigate('/dashboard/inventory/stock-levels')}
            >
              View Stock Levels
            </Button>
          </Col>
          <Col xs={24} sm={8}>
            <Button 
              block 
              size="large"
              onClick={() => navigate('/dashboard/inventory/transactions')}
            >
              View Transactions
            </Button>
          </Col>
          <Col xs={24} sm={8}>
            <Button 
              block 
              size="large"
              onClick={() => navigate('/dashboard/inventory/transfers')}
            >
              View Transfers
            </Button>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default InventoryDashboard;

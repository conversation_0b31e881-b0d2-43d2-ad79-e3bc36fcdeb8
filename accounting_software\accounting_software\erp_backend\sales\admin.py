from django.contrib import admin
from django.forms import ModelForm, Select
from django.utils.html import format_html
from django.utils import timezone
from .models import (
    ProductCategory, Product, Payment, PaymentTerm, Invoice, InvoiceLineItem
)
from sales_tax.models import SalesTax


# Customer admin is now in contacts app - references moved to contacts.Contact

# Customer admin moved to contacts app - do not register here to avoid conflicts
# The CustomerAdmin class below is kept for reference only and is not registered
class CustomerAdminReference(admin.ModelAdmin):
    """
    DEPRECATED: Customer admin has been moved to contacts app.
    This is kept for reference but not registered to avoid conflicts.
    Use /admin/contacts/customer/ instead of /admin/sales/customer/
    
    Customer model is now accessed via contacts.Contact with contact_type='customer'
    """
    # This class is kept for documentation purposes only
    pass


@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent_category', 'is_active', 'created_at']
    list_filter = ['is_active', 'parent_category', 'created_at']
    search_fields = ['name', 'description']


class ProductAdminForm(ModelForm):
    """Custom form for Product admin with enhanced fields"""
    
    class Meta:
        model = Product
        fields = '__all__'
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add sales tax choices for tax rate field
        if 'sales_tax_category' in self.fields:
            output_taxes = SalesTax.objects.filter(tax_type='output').order_by('rate')
            choices = [('', 'No Tax')] + [(f"{tax.rate}%", f"{tax.description} ({tax.rate}%)") for tax in output_taxes]
            self.fields['sales_tax_category'].widget = Select(choices=choices)
            self.fields['sales_tax_category'].help_text = "Select output tax rate for sales"


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    form = ProductAdminForm
    list_display = [
        'name', 'sku', 'product_type', 'category', 
        'get_unit_price_display', 'get_cost_price_display', 'get_margin_display',
        'get_current_stock', 'get_income_account_display', 'status', 'created_at'
    ]
    list_filter = ['product_type', 'category', 'status', 'taxable', 'track_inventory', 'created_at']
    search_fields = [
        'name', 'sku', 'description', 
        'income_account_gl__account_name', 'expense_account_gl__account_name', 
        'inventory_asset_account_gl__account_name'
    ]
    readonly_fields = [
        'product_id', 'get_margin_display', 'get_markup_display', 
        'get_current_stock', 'price_last_updated_at', 'created_at', 'updated_at'
    ]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('product_id', 'name', 'sku', 'product_type', 'category', 'description')
        }),
        ('💰 Sales Editable Fields', {
            'fields': ('unit_price', 'sales_tax_category'),
            'description': '🔹 Sales Department Access: You can only edit selling price and tax rates. Other product details are managed by the Purchase Department.',
        }),
        ('📊 Purchase Department Fields', {
            'fields': ('cost_price', 'minimum_selling_price', 'preferred_vendor'),
            'description': 'Managed by Purchase Department - Read Only for Sales',
        }),
        ('📈 Calculated Fields', {
            'fields': ('get_margin_display', 'get_markup_display'),
            'description': 'Auto-calculated based on cost and selling price',
        }),
        ('🏦 GL Account Integration', {
            'fields': ('income_account_gl', 'expense_account_gl', 'inventory_asset_account_gl'),
            'description': 'Link to Chart of Accounts for proper GL integration'
        }),
        ('📦 Inventory Information', {
            'fields': (
                'track_inventory', 'get_current_stock', 'reorder_point',
                'quantity_on_hand', 'quantity_on_purchase_order', 'quantity_on_sales_order'
            ),
            'description': 'Inventory tracking and stock levels'
        }),
        ('⚙️ Settings', {
            'fields': ('status', 'taxable')
        }),
        ('📅 Price History', {
            'fields': ('price_effective_date', 'price_last_updated_by', 'price_last_updated_at'),
        }),
        ('📝 Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by'),
        }),
    )
    
    def get_unit_price_display(self, obj):
        """Display unit price with USD currency symbol"""
        if obj.unit_price is not None:
            return format_html('<span style="color: green; font-weight: bold;">$ {}</span>', f'{float(obj.unit_price):,.2f}')
        return format_html('<span style="color: orange;">Not Set</span>')
    get_unit_price_display.short_description = "💰 Selling Price"
    get_unit_price_display.admin_order_field = 'unit_price'
    
    def get_cost_price_display(self, obj):
        """Display cost price with USD currency symbol"""
        if obj.cost_price is not None:
            return format_html('<span style="color: blue;">$ {}</span>', f'{float(obj.cost_price):,.2f}')
        return format_html('<span style="color: red;">Not Set</span>')
    get_cost_price_display.short_description = "📊 Cost Price"
    get_cost_price_display.admin_order_field = 'cost_price'
    
    def get_margin_display(self, obj):
        """Display margin amount and percentage"""
        if obj.cost_price and obj.cost_price > 0 and obj.unit_price is not None:
            margin_amount = float(obj.unit_price) - float(obj.cost_price)
            margin_percent = ((float(obj.unit_price) - float(obj.cost_price)) / float(obj.cost_price)) * 100
            color = 'green' if margin_amount > 0 else 'red'
            return format_html(
                '<span style="color: {}; font-weight: bold;">$ {} ({}%)</span>',
                color, f'{margin_amount:,.2f}', f'{margin_percent:.1f}'
            )
        return format_html('<span style="color: orange;">Cost price needed</span>')
    get_margin_display.short_description = "📈 Margin (Amount & %)"
    
    def get_markup_display(self, obj):
        """Display markup percentage"""
        if obj.cost_price and obj.cost_price > 0:
            markup = ((obj.unit_price - obj.cost_price) / obj.cost_price) * 100
            return f"{markup:.1f}%"
        return "Cost price needed"
    get_markup_display.short_description = "📊 Markup %"
    
    def get_current_stock(self, obj):
        """Display current stock with status indicator"""
        if obj.track_inventory:
            stock = obj.quantity_on_hand
            if stock <= 0:
                color = 'red'
                status = '⚠️ Out of Stock'
            elif stock <= obj.reorder_point:
                color = 'orange'
                status = '⚠️ Low Stock'
            else:
                color = 'green'
                status = '✅ In Stock'
            return format_html(
                '<span style="color: {}; font-weight: bold;">{} units</span><br/><small>{}</small>',
                color, stock, status
            )
        return "Not tracked"
    get_current_stock.short_description = "📦 Current Stock"
    
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Customize dropdown options for GL accounts"""
        if db_field.name == "income_account_gl":
            # Only show Revenue accounts
            kwargs["queryset"] = db_field.remote_field.model.objects.filter(
                account_type__type='REVENUE', 
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        elif db_field.name == "expense_account_gl":
            # Only show Expense accounts  
            kwargs["queryset"] = db_field.remote_field.model.objects.filter(
                account_type__type='EXPENSE',
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        elif db_field.name == "inventory_asset_account_gl":
            # Only show Asset accounts suitable for inventory
            kwargs["queryset"] = db_field.remote_field.model.objects.filter(
                account_type__type='ASSET',
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def get_income_account_display(self, obj):
        """Display income account with number and name"""
        if obj.income_account_gl:
            return f"{obj.income_account_gl.account_number} - {obj.income_account_gl.account_name}"
        return "Not Assigned"
    get_income_account_display.short_description = "Sales Account"
    
    def get_expense_account_display(self, obj):
        """Display expense account with number and name"""
        if obj.expense_account_gl:
            return f"{obj.expense_account_gl.account_number} - {obj.expense_account_gl.account_name}"
        return "Not Assigned"
    get_expense_account_display.short_description = "COGS Account"
    
    def get_inventory_account_display(self, obj):
        """Display inventory account with number and name"""
        if obj.inventory_asset_account_gl:
            return f"{obj.inventory_asset_account_gl.account_number} - {obj.inventory_asset_account_gl.account_name}"
        return "Not Assigned"
    get_inventory_account_display.short_description = "Inventory Account"
    
    def save_model(self, request, obj, form, change):
        """Override save to track price changes"""
        if change:
            # Check if unit_price changed
            try:
                original = Product.objects.get(pk=obj.pk)
                if original.unit_price != obj.unit_price:
                    obj.price_last_updated_by = request.user
                    obj.price_last_updated_at = timezone.now()
                    if not obj.price_effective_date:
                        obj.price_effective_date = timezone.now().date()
            except Product.DoesNotExist:
                pass
        
        super().save_model(request, obj, form, change)


# New Invoice System - Built from scratch

class InvoiceLineItemInline(admin.TabularInline):
    """
    Inline admin for Invoice Line Items
    Allows editing line items directly within the invoice form
    """
    model = InvoiceLineItem
    extra = 1
    fields = [
        'product', 'description', 'quantity', 'unit_price',
        'taxable', 'tax_rate', 'line_total'
    ]
    readonly_fields = ['line_total']

    def get_formset(self, request, obj=None, **kwargs):
        """Customize the formset"""
        formset = super().get_formset(request, obj, **kwargs)
        return formset


@admin.register(InvoiceLineItem)
class InvoiceLineItemAdmin(admin.ModelAdmin):
    """
    Django Admin interface for Invoice Line Items
    """
    list_display = [
        'get_invoice_number', 'get_product_code', 'get_product_name',
        'quantity', 'unit_price', 'line_total', 'taxable', 'tax_rate'
    ]
    list_filter = [
        'taxable', 'product__type', 'invoice__status', 'created_at'
    ]
    search_fields = [
        'invoice__invoice_number', 'product__code', 'product__name', 'description'
    ]
    readonly_fields = ['line_total', 'created_at']

    fieldsets = (
        ('📋 Line Item Information', {
            'fields': ('invoice', 'product', 'description')
        }),
        ('📊 Quantity & Pricing', {
            'fields': ('quantity', 'unit_price', 'line_total')
        }),
        ('💰 Tax Information', {
            'fields': ('taxable', 'tax_rate')
        }),
        ('🔧 Metadata', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    def get_invoice_number(self, obj):
        """Display invoice number with link"""
        if obj.invoice:
            return format_html(
                '<a href="/admin/sales/invoice/{}/change/">{}</a>',
                obj.invoice.id, obj.invoice.invoice_number
            )
        return "No Invoice"
    get_invoice_number.short_description = "📋 Invoice"
    get_invoice_number.admin_order_field = 'invoice__invoice_number'

    def get_product_code(self, obj):
        """Display product code"""
        return obj.product.code if obj.product else "No Product"
    get_product_code.short_description = "🏷️ Product Code"
    get_product_code.admin_order_field = 'product__code'

    def get_product_name(self, obj):
        """Display product name"""
        return obj.product.name if obj.product else "No Product"
    get_product_name.short_description = "📦 Product Name"
    get_product_name.admin_order_field = 'product__name'


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    """
    Django Admin interface for the new Invoice model
    Built from scratch following QuickBooks/ERP standards
    """
    inlines = [InvoiceLineItemInline]

    list_display = [
        'invoice_number', 'get_customer_display', 'invoice_date', 'due_date',
        'get_status_display', 'get_payment_terms_display', 'get_total_amount_display',
        'get_days_overdue_display', 'created_at'
    ]
    list_filter = [
        'status', 'invoice_date', 'due_date', 'payment_terms',
        'created_at', 'sales_rep'
    ]
    search_fields = [
        'invoice_number', 'customer__name', 'customer__email',
        'po_number', 'sales_rep__username'
    ]
    readonly_fields = [
        'invoice_id', 'invoice_number', 'get_total_amount_display',
        'get_days_overdue_display', 'created_at', 'updated_at'
    ]
    date_hierarchy = 'invoice_date'
    ordering = ['-invoice_date', '-created_at']

    fieldsets = (
        ('📋 Invoice Information', {
            'fields': ('invoice_id', 'invoice_number', 'status')
        }),
        ('👤 Customer Information', {
            'fields': ('customer', 'sales_rep')
        }),
        ('📅 Date Information', {
            'fields': ('invoice_date', 'due_date', 'payment_terms')
        }),
        ('📝 Reference Information', {
            'fields': ('po_number',)
        }),
        ('💰 Financial Summary', {
            'fields': ('get_total_amount_display', 'get_days_overdue_display'),
            'description': 'Financial calculations (will be updated when line items are added)'
        }),
        ('🔧 Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )

    def get_customer_display(self, obj):
        """Display customer name with contact type"""
        if obj.customer:
            return format_html(
                '<strong>{}</strong><br/><small style="color: gray;">{}</small>',
                obj.customer.name,
                obj.customer.email or 'No email'
            )
        return "No Customer"
    get_customer_display.short_description = "👤 Customer"
    get_customer_display.admin_order_field = 'customer__name'

    def get_status_display(self, obj):
        """Display status with color coding"""
        status_colors = {
            'draft': 'gray',
            'sent': 'blue',
            'paid': 'green',
            'overdue': 'red',
            'cancelled': 'orange',
            'partial': 'purple'
        }
        color = status_colors.get(obj.status, 'black')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    get_status_display.short_description = "📊 Status"
    get_status_display.admin_order_field = 'status'

    def get_payment_terms_display(self, obj):
        """Display payment terms with days"""
        if obj.payment_terms:
            return f"{obj.payment_terms.name} ({obj.payment_terms.days} days)"
        return "No Terms"
    get_payment_terms_display.short_description = "💳 Payment Terms"
    get_payment_terms_display.admin_order_field = 'payment_terms__days'

    def get_total_amount_display(self, obj):
        """Display total amount (placeholder until line items are added)"""
        total = obj.total_amount
        if total > 0:
            return format_html(
                '<span style="color: green; font-weight: bold; font-size: 14px;">$ {}</span>',
                f'{float(total):,.2f}'
            )
        return format_html(
            '<span style="color: orange;">$ 0.00</span><br/><small>No line items</small>'
        )
    get_total_amount_display.short_description = "💰 Total Amount"

    def get_days_overdue_display(self, obj):
        """Display overdue status with days"""
        if obj.is_overdue:
            days = obj.days_overdue
            return format_html(
                '<span style="color: red; font-weight: bold;">⚠️ {} days overdue</span>',
                days
            )
        elif obj.status == 'paid':
            return format_html('<span style="color: green;">✅ Paid</span>')
        else:
            return format_html('<span style="color: blue;">📅 On time</span>')
    get_days_overdue_display.short_description = "⏰ Due Status"

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Customize dropdown options"""
        if db_field.name == "customer":
            # Only show customer contacts
            kwargs["queryset"] = db_field.remote_field.model.objects.filter(
                contact_type='customer'
            ).order_by('name')
        elif db_field.name == "payment_terms":
            # Only show active payment terms
            kwargs["queryset"] = db_field.remote_field.model.objects.filter(
                is_active=True
            ).order_by('days', 'name')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def save_model(self, request, obj, form, change):
        """Override save to set created_by for new invoices"""
        if not change:  # New invoice
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ['payment_id', 'customer', 'payment_date', 'amount', 'payment_method']
    list_filter = ['payment_method', 'payment_date', 'created_at']
    search_fields = ['customer__name', 'reference_number']
    readonly_fields = ['payment_id', 'created_at', 'updated_at']
    date_hierarchy = 'payment_date'

    fieldsets = (
        ('Basic Information', {
            'fields': ('payment_id', 'customer', 'payment_date', 'amount')
        }),
        ('Payment Details', {
            'fields': ('payment_method', 'reference_number', 'notes')
        }),
        ('Deposit Information', {
            'fields': ('deposit_to_account',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )


# Estimate admin classes removed - will be replaced with new system


@admin.register(PaymentTerm)
class PaymentTermAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'days', 'is_default', 'is_active', 'created_at']
    list_filter = ['is_default', 'is_active', 'created_at']
    search_fields = ['name', 'code', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'days', 'description')
        }),
        ('Settings', {
            'fields': ('is_default', 'is_active')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )


# SalesOrder and DeliveryNote admin classes removed - will be replaced with new system

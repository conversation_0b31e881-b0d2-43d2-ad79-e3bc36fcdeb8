from django.contrib import admin
from .models import PurchaseOrder, PurchaseOrderLineItem, VendorPayment, PaymentTerm


# Temporarily disabled for inventory migration
# class PurchaseOrderLineItemInline(admin.TabularInline):
#     """Inline admin for Purchase Order Line Items"""
#     model = PurchaseOrderLineItem
#     extra = 1
#     fields = ('product', 'description', 'quantity', 'unit_of_measure', 'unit_price', 'discount_percent', 'line_total', 'taxable', 'notes')
#     readonly_fields = ('line_total',)


# @admin.register(PurchaseOrder)
# class PurchaseOrderAdmin(admin.ModelAdmin):
#     list_display = ['po_number', 'vendor', 'po_date', 'total_amount', 'status', 'created_at']
#     list_filter = ['status', 'po_date', 'created_at']
#     search_fields = ['po_number', 'vendor__name', 'reference_number']
#     readonly_fields = ['po_id', 'po_number', 'subtotal', 'total_amount', 'balance_due', 'created_at', 'updated_at']
#     # inlines = [PurchaseOrderLineItemInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('po_id', 'po_number', 'vendor', 'po_date', 'expected_date', 'reference_number')
        }),
        ('Buyer Information', {
            'fields': ('buyer_name', 'buyer_email', 'buyer_phone')
        }),
        ('Financial Information', {
            'fields': ('subtotal', 'discount_percent', 'discount_amount', 'tax_amount', 'total_amount', 'amount_received', 'balance_due')
        }),
        ('Settings', {
            'fields': ('status', 'payment_terms')
        }),
        ('Additional Information', {
            'fields': ('memo', 'notes', 'ship_to_address', 'email_sent', 'email_sent_date', 'acknowledged_date')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )


@admin.register(PurchaseOrderLineItem)
class PurchaseOrderLineItemAdmin(admin.ModelAdmin):
    list_display = ['purchase_order', 'product', 'description', 'quantity', 'unit_of_measure', 'unit_price', 'line_total']
    list_filter = ['purchase_order__status', 'taxable', 'unit_of_measure']
    search_fields = ['purchase_order__po_number', 'product__name', 'description']


@admin.register(PaymentTerm)
class PaymentTermAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'days', 'is_default', 'is_active']
    list_filter = ['is_default', 'is_active']
    search_fields = ['name', 'code']


@admin.register(VendorPayment)
class VendorPaymentAdmin(admin.ModelAdmin):
    list_display = ['vendor', 'purchase_order', 'payment_date', 'amount', 'payment_method']
    list_filter = ['payment_method', 'payment_date']
    search_fields = ['vendor__name', 'purchase_order__po_number', 'reference_number']

/**
 * Enterprise Inventory Service
 * Following our established patterns with comprehensive inventory management
 */

import api from '../../../services/api';

// ==================== INTERFACES ====================

export interface Warehouse {
  warehouse_id: number;
  code: string;
  name: string;
  warehouse_type: 'MAIN' | 'BRANCH' | 'VIRTUAL' | 'DAMAGED';
  address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country: string;
  phone?: string;
  email?: string;
  manager?: number;
  is_active: boolean;
  allow_negative_stock: boolean;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface StockLevel {
  stock_level_id: number;
  product: number;
  warehouse: number;
  product_code: string;
  product_name: string;
  warehouse_code: string;
  warehouse_name: string;
  quantity_on_hand: string;
  quantity_reserved: string;
  quantity_on_order: string;
  reorder_point: string;
  reorder_quantity: string;
  max_stock_level: string;
  average_cost: string;
  last_cost: string;
  standard_cost: string;
  available_quantity: string;
  total_committed: string;
  is_below_reorder_point: boolean;
  is_out_of_stock: boolean;
  stock_value_average: string;
  stock_value_last: string;
  stock_value_standard: string;
  last_updated: string;
  last_transaction_date?: string;
  last_counted_date?: string;
}

export interface StockTransaction {
  transaction_id: number;
  product: number;
  warehouse: number;
  product_code: string;
  product_name: string;
  warehouse_code: string;
  warehouse_name: string;
  transaction_type: 'RECEIPT' | 'ISSUE' | 'ADJUSTMENT' | 'TRANSFER_OUT' | 'TRANSFER_IN' | 'RETURN' | 'DAMAGE' | 'CYCLE_COUNT';
  quantity: string;
  unit_cost: string;
  total_cost: string;
  reference_type?: string;
  reference_number?: string;
  reference_id?: number;
  description?: string;
  batch_number?: string;
  serial_number?: string;
  expiry_date?: string;
  balance_quantity: string;
  balance_value: string;
  transaction_date: string;
  created_at: string;
  created_by: number;
  created_by_name: string;
}

export interface StockAdjustment {
  adjustment_id: number;
  adjustment_number: string;
  warehouse: number;
  warehouse_code: string;
  warehouse_name: string;
  reason: 'CYCLE_COUNT' | 'DAMAGE' | 'THEFT' | 'EXPIRED' | 'FOUND' | 'OTHER';
  description?: string;
  status: 'DRAFT' | 'POSTED' | 'CANCELLED';
  total_items: number;
  total_adjustment_value: string;
  adjustment_date: string;
  created_at: string;
  updated_at: string;
  created_by: number;
  created_by_name: string;
  posted_at?: string;
  posted_by?: number;
  posted_by_name?: string;
  items: StockAdjustmentItem[];
}

export interface StockAdjustmentItem {
  item_id: number;
  product: number;
  product_code: string;
  product_name: string;
  system_quantity: string;
  physical_quantity: string;
  adjustment_quantity: string;
  unit_cost: string;
  adjustment_value: string;
  notes?: string;
}

export interface StockTransfer {
  transfer_id: number;
  transfer_number: string;
  from_warehouse: number;
  to_warehouse: number;
  from_warehouse_code: string;
  from_warehouse_name: string;
  to_warehouse_code: string;
  to_warehouse_name: string;
  transfer_date: string;
  expected_date?: string;
  description?: string;
  status: 'DRAFT' | 'IN_TRANSIT' | 'RECEIVED' | 'CANCELLED';
  total_items: number;
  total_quantity: string;
  total_value: string;
  created_at: string;
  updated_at: string;
  created_by: number;
  created_by_name: string;
  shipped_at?: string;
  shipped_by?: number;
  shipped_by_name?: string;
  received_at?: string;
  received_by?: number;
  received_by_name?: string;
  items: StockTransferItem[];
}

export interface StockTransferItem {
  item_id: number;
  product: number;
  product_code: string;
  product_name: string;
  quantity: string;
  unit_cost: string;
  total_cost: string;
  notes?: string;
}

export interface InventorySummary {
  total_warehouses: number;
  total_products: number;
  total_quantity: string;
  total_value: string;
  low_stock_items: number;
  out_of_stock_items: number;
  pending_transfers: number;
  pending_adjustments: number;
}

export interface WarehouseSummary {
  warehouse_id: number;
  warehouse_code: string;
  warehouse_name: string;
  total_products: number;
  total_quantity: string;
  total_value: string;
  low_stock_items: number;
  out_of_stock_items: number;
}

// ==================== API RESPONSES ====================

interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// ==================== SERVICE CLASS ====================

class InventoryService {
  private readonly baseUrl = '/api/inventory';

  // ==================== WAREHOUSES ====================

  async getWarehouses(): Promise<Warehouse[]> {
    try {
      const response = await api.get<PaginatedResponse<Warehouse>>(`${this.baseUrl}/warehouses/`);
      return response.data.results;
    } catch (error) {
      console.error('❌ Error fetching warehouses:', error);
      throw new Error('Failed to fetch warehouses');
    }
  }

  async getWarehouse(id: number): Promise<Warehouse> {
    try {
      const response = await api.get<Warehouse>(`${this.baseUrl}/warehouses/${id}/`);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching warehouse:', error);
      throw new Error('Failed to fetch warehouse');
    }
  }

  async createWarehouse(warehouse: Partial<Warehouse>): Promise<Warehouse> {
    try {
      const response = await api.post<Warehouse>(`${this.baseUrl}/warehouses/`, warehouse);
      return response.data;
    } catch (error) {
      console.error('❌ Error creating warehouse:', error);
      throw new Error('Failed to create warehouse');
    }
  }

  async updateWarehouse(id: number, warehouse: Partial<Warehouse>): Promise<Warehouse> {
    try {
      const response = await api.put<Warehouse>(`${this.baseUrl}/warehouses/${id}/`, warehouse);
      return response.data;
    } catch (error) {
      console.error('❌ Error updating warehouse:', error);
      throw new Error('Failed to update warehouse');
    }
  }

  async deleteWarehouse(id: number): Promise<void> {
    try {
      await api.delete(`${this.baseUrl}/warehouses/${id}/`);
    } catch (error) {
      console.error('❌ Error deleting warehouse:', error);
      throw new Error('Failed to delete warehouse');
    }
  }

  async getWarehouseSummary(): Promise<WarehouseSummary[]> {
    try {
      const response = await api.get<WarehouseSummary[]>(`${this.baseUrl}/warehouses/summary/`);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching warehouse summary:', error);
      throw new Error('Failed to fetch warehouse summary');
    }
  }

  // ==================== STOCK LEVELS ====================

  async getStockLevels(params?: {
    warehouse?: number;
    product?: number;
    low_stock?: boolean;
    out_of_stock?: boolean;
    search?: string;
    page?: number;
    page_size?: number;
  }): Promise<PaginatedResponse<StockLevel>> {
    try {
      const response = await api.get<PaginatedResponse<StockLevel>>(`${this.baseUrl}/stock-levels/`, { params });
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching stock levels:', error);
      throw new Error('Failed to fetch stock levels');
    }
  }

  async getStockLevel(id: number): Promise<StockLevel> {
    try {
      const response = await api.get<StockLevel>(`${this.baseUrl}/stock-levels/${id}/`);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching stock level:', error);
      throw new Error('Failed to fetch stock level');
    }
  }

  async getLowStockItems(): Promise<StockLevel[]> {
    try {
      const response = await api.get<StockLevel[]>(`${this.baseUrl}/stock-levels/low_stock/`);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching low stock items:', error);
      throw new Error('Failed to fetch low stock items');
    }
  }

  async getOutOfStockItems(): Promise<StockLevel[]> {
    try {
      const response = await api.get<StockLevel[]>(`${this.baseUrl}/stock-levels/out_of_stock/`);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching out of stock items:', error);
      throw new Error('Failed to fetch out of stock items');
    }
  }

  async getInventorySummary(): Promise<InventorySummary> {
    try {
      const response = await api.get<InventorySummary>(`${this.baseUrl}/stock-levels/summary/`);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching inventory summary:', error);
      throw new Error('Failed to fetch inventory summary');
    }
  }

  async reserveStock(stockLevelId: number, quantity: number): Promise<void> {
    try {
      await api.post(`${this.baseUrl}/stock-levels/${stockLevelId}/reserve_stock/`, { quantity });
    } catch (error) {
      console.error('❌ Error reserving stock:', error);
      throw new Error('Failed to reserve stock');
    }
  }

  async releaseReservation(stockLevelId: number, quantity: number): Promise<void> {
    try {
      await api.post(`${this.baseUrl}/stock-levels/${stockLevelId}/release_reservation/`, { quantity });
    } catch (error) {
      console.error('❌ Error releasing reservation:', error);
      throw new Error('Failed to release reservation');
    }
  }

  // ==================== STOCK TRANSACTIONS ====================

  async getStockTransactions(params?: {
    transaction_type?: string;
    warehouse?: number;
    product?: number;
    reference_type?: string;
    start_date?: string;
    end_date?: string;
    search?: string;
    page?: number;
    page_size?: number;
  }): Promise<PaginatedResponse<StockTransaction>> {
    try {
      const response = await api.get<PaginatedResponse<StockTransaction>>(`${this.baseUrl}/stock-transactions/`, { params });
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching stock transactions:', error);
      throw new Error('Failed to fetch stock transactions');
    }
  }

  async getStockTransaction(id: number): Promise<StockTransaction> {
    try {
      const response = await api.get<StockTransaction>(`${this.baseUrl}/stock-transactions/${id}/`);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching stock transaction:', error);
      throw new Error('Failed to fetch stock transaction');
    }
  }

  async createStockTransaction(transaction: Partial<StockTransaction>): Promise<StockTransaction> {
    try {
      const response = await api.post<StockTransaction>(`${this.baseUrl}/stock-transactions/`, transaction);
      return response.data;
    } catch (error) {
      console.error('❌ Error creating stock transaction:', error);
      throw new Error('Failed to create stock transaction');
    }
  }

  async getTransactionsByProduct(productId: number): Promise<StockTransaction[]> {
    try {
      const response = await api.get<StockTransaction[]>(`${this.baseUrl}/stock-transactions/by_product/`, {
        params: { product_id: productId }
      });
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching transactions by product:', error);
      throw new Error('Failed to fetch transactions by product');
    }
  }

  async getTransactionsByWarehouse(warehouseId: number): Promise<StockTransaction[]> {
    try {
      const response = await api.get<StockTransaction[]>(`${this.baseUrl}/stock-transactions/by_warehouse/`, {
        params: { warehouse_id: warehouseId }
      });
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching transactions by warehouse:', error);
      throw new Error('Failed to fetch transactions by warehouse');
    }
  }

  async getTransactionStats(): Promise<any> {
    try {
      const response = await api.get(`${this.baseUrl}/stock-transactions/stats/`);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching transaction stats:', error);
      throw new Error('Failed to fetch transaction stats');
    }
  }

  // ==================== STOCK ADJUSTMENTS ====================

  async getStockAdjustments(params?: {
    status?: string;
    reason?: string;
    warehouse?: number;
    search?: string;
    page?: number;
    page_size?: number;
  }): Promise<PaginatedResponse<StockAdjustment>> {
    try {
      const response = await api.get<PaginatedResponse<StockAdjustment>>(`${this.baseUrl}/stock-adjustments/`, { params });
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching stock adjustments:', error);
      throw new Error('Failed to fetch stock adjustments');
    }
  }

  async getStockAdjustment(id: number): Promise<StockAdjustment> {
    try {
      const response = await api.get<StockAdjustment>(`${this.baseUrl}/stock-adjustments/${id}/`);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching stock adjustment:', error);
      throw new Error('Failed to fetch stock adjustment');
    }
  }

  async createStockAdjustment(adjustment: Partial<StockAdjustment>): Promise<StockAdjustment> {
    try {
      const response = await api.post<StockAdjustment>(`${this.baseUrl}/stock-adjustments/`, adjustment);
      return response.data;
    } catch (error) {
      console.error('❌ Error creating stock adjustment:', error);
      throw new Error('Failed to create stock adjustment');
    }
  }

  async updateStockAdjustment(id: number, adjustment: Partial<StockAdjustment>): Promise<StockAdjustment> {
    try {
      const response = await api.put<StockAdjustment>(`${this.baseUrl}/stock-adjustments/${id}/`, adjustment);
      return response.data;
    } catch (error) {
      console.error('❌ Error updating stock adjustment:', error);
      throw new Error('Failed to update stock adjustment');
    }
  }

  async postStockAdjustment(id: number): Promise<void> {
    try {
      await api.post(`${this.baseUrl}/stock-adjustments/${id}/post_adjustment/`);
    } catch (error) {
      console.error('❌ Error posting stock adjustment:', error);
      throw new Error('Failed to post stock adjustment');
    }
  }

  async getPendingAdjustments(): Promise<StockAdjustment[]> {
    try {
      const response = await api.get<StockAdjustment[]>(`${this.baseUrl}/stock-adjustments/pending/`);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching pending adjustments:', error);
      throw new Error('Failed to fetch pending adjustments');
    }
  }

  // ==================== STOCK TRANSFERS ====================

  async getStockTransfers(params?: {
    status?: string;
    from_warehouse?: number;
    to_warehouse?: number;
    search?: string;
    page?: number;
    page_size?: number;
  }): Promise<PaginatedResponse<StockTransfer>> {
    try {
      const response = await api.get<PaginatedResponse<StockTransfer>>(`${this.baseUrl}/stock-transfers/`, { params });
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching stock transfers:', error);
      throw new Error('Failed to fetch stock transfers');
    }
  }

  async getStockTransfer(id: number): Promise<StockTransfer> {
    try {
      const response = await api.get<StockTransfer>(`${this.baseUrl}/stock-transfers/${id}/`);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching stock transfer:', error);
      throw new Error('Failed to fetch stock transfer');
    }
  }

  async createStockTransfer(transfer: Partial<StockTransfer>): Promise<StockTransfer> {
    try {
      const response = await api.post<StockTransfer>(`${this.baseUrl}/stock-transfers/`, transfer);
      return response.data;
    } catch (error) {
      console.error('❌ Error creating stock transfer:', error);
      throw new Error('Failed to create stock transfer');
    }
  }

  async updateStockTransfer(id: number, transfer: Partial<StockTransfer>): Promise<StockTransfer> {
    try {
      const response = await api.put<StockTransfer>(`${this.baseUrl}/stock-transfers/${id}/`, transfer);
      return response.data;
    } catch (error) {
      console.error('❌ Error updating stock transfer:', error);
      throw new Error('Failed to update stock transfer');
    }
  }

  async shipStockTransfer(id: number): Promise<void> {
    try {
      await api.post(`${this.baseUrl}/stock-transfers/${id}/ship_transfer/`);
    } catch (error) {
      console.error('❌ Error shipping stock transfer:', error);
      throw new Error('Failed to ship stock transfer');
    }
  }

  async receiveStockTransfer(id: number): Promise<void> {
    try {
      await api.post(`${this.baseUrl}/stock-transfers/${id}/receive_transfer/`);
    } catch (error) {
      console.error('❌ Error receiving stock transfer:', error);
      throw new Error('Failed to receive stock transfer');
    }
  }

  async getInTransitTransfers(): Promise<StockTransfer[]> {
    try {
      const response = await api.get<StockTransfer[]>(`${this.baseUrl}/stock-transfers/in_transit/`);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching in transit transfers:', error);
      throw new Error('Failed to fetch in transit transfers');
    }
  }

  async getPendingTransfers(): Promise<StockTransfer[]> {
    try {
      const response = await api.get<StockTransfer[]>(`${this.baseUrl}/stock-transfers/pending/`);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching pending transfers:', error);
      throw new Error('Failed to fetch pending transfers');
    }
  }
}

export const inventoryService = new InventoryService();

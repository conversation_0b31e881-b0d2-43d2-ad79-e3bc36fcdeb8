# Generated by Django 4.2.21 on 2025-06-29 13:23

import datetime
from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('Pricing', '0004_product_auto_pricing_enabled_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='StockAdjustment',
            fields=[
                ('adjustment_id', models.AutoField(primary_key=True, serialize=False)),
                ('adjustment_number', models.CharField(max_length=50, unique=True)),
                ('reason', models.CharField(choices=[('CYCLE_COUNT', 'Cycle Count'), ('DAMAGE', 'Damaged Goods'), ('THEFT', 'Theft/Loss'), ('EXPIRED', 'Expired Goods'), ('FOUND', 'Found Goods'), ('OTHER', 'Other')], max_length=20)),
                ('description', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('POSTED', 'Posted'), ('CANCELLED', 'Cancelled')], default='DRAFT', max_length=20)),
                ('total_items', models.PositiveIntegerField(default=0)),
                ('total_adjustment_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('adjustment_date', models.DateField(default=datetime.date.today)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('posted_at', models.DateTimeField(blank=True, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='adjustments_created', to=settings.AUTH_USER_MODEL)),
                ('posted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='adjustments_posted', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'stock_adjustments',
                'ordering': ['-adjustment_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockTransfer',
            fields=[
                ('transfer_id', models.AutoField(primary_key=True, serialize=False)),
                ('transfer_number', models.CharField(max_length=50, unique=True)),
                ('transfer_date', models.DateField(default=datetime.date.today)),
                ('expected_date', models.DateField(blank=True, null=True)),
                ('description', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('IN_TRANSIT', 'In Transit'), ('RECEIVED', 'Received'), ('CANCELLED', 'Cancelled')], default='DRAFT', max_length=20)),
                ('total_items', models.PositiveIntegerField(default=0)),
                ('total_quantity', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=15)),
                ('total_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('shipped_at', models.DateTimeField(blank=True, null=True)),
                ('received_at', models.DateTimeField(blank=True, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'stock_transfers',
                'ordering': ['-transfer_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockValuation',
            fields=[
                ('valuation_id', models.AutoField(primary_key=True, serialize=False)),
                ('valuation_date', models.DateField()),
                ('method', models.CharField(choices=[('FIFO', 'First In, First Out'), ('LIFO', 'Last In, First Out'), ('AVERAGE', 'Weighted Average'), ('STANDARD', 'Standard Cost')], max_length=10)),
                ('total_items', models.PositiveIntegerField(default=0)),
                ('total_quantity', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=15)),
                ('total_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('FINALIZED', 'Finalized')], default='DRAFT', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('finalized_at', models.DateTimeField(blank=True, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='valuations_created', to=settings.AUTH_USER_MODEL)),
                ('finalized_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='valuations_finalized', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'stock_valuations',
                'ordering': ['-valuation_date'],
            },
        ),
        migrations.CreateModel(
            name='Warehouse',
            fields=[
                ('warehouse_id', models.AutoField(primary_key=True, serialize=False)),
                ('code', models.CharField(help_text='Warehouse code (e.g., MAIN, BR001)', max_length=20, unique=True)),
                ('name', models.CharField(max_length=100)),
                ('warehouse_type', models.CharField(choices=[('MAIN', 'Main Warehouse'), ('BRANCH', 'Branch Location'), ('VIRTUAL', 'Virtual/Consignment'), ('DAMAGED', 'Damaged Goods')], default='MAIN', max_length=10)),
                ('address', models.TextField(blank=True)),
                ('city', models.CharField(blank=True, max_length=50)),
                ('state', models.CharField(blank=True, max_length=50)),
                ('postal_code', models.CharField(blank=True, max_length=20)),
                ('country', models.CharField(default='USA', max_length=50)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('is_active', models.BooleanField(default=True)),
                ('allow_negative_stock', models.BooleanField(default=False, help_text='Allow negative inventory levels')),
                ('is_default', models.BooleanField(default=False, help_text='Default warehouse for new transactions')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='warehouses_created', to=settings.AUTH_USER_MODEL)),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_warehouses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'warehouses',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='StockValuationItem',
            fields=[
                ('item_id', models.AutoField(primary_key=True, serialize=False)),
                ('quantity', models.DecimalField(decimal_places=4, max_digits=15)),
                ('unit_cost', models.DecimalField(decimal_places=4, max_digits=15)),
                ('total_value', models.DecimalField(decimal_places=2, max_digits=15)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Pricing.product')),
                ('valuation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.stockvaluation')),
            ],
            options={
                'db_table': 'stock_valuation_items',
            },
        ),
        migrations.AddField(
            model_name='stockvaluation',
            name='warehouse',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='valuations', to='inventory.warehouse'),
        ),
        migrations.CreateModel(
            name='StockTransferItem',
            fields=[
                ('item_id', models.AutoField(primary_key=True, serialize=False)),
                ('quantity', models.DecimalField(decimal_places=4, max_digits=15, validators=[django.core.validators.MinValueValidator(0.0001)])),
                ('unit_cost', models.DecimalField(decimal_places=4, max_digits=15)),
                ('total_cost', models.DecimalField(decimal_places=2, max_digits=15)),
                ('notes', models.TextField(blank=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Pricing.product')),
                ('transfer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.stocktransfer')),
            ],
            options={
                'db_table': 'stock_transfer_items',
            },
        ),
        migrations.AddField(
            model_name='stocktransfer',
            name='from_warehouse',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_out', to='inventory.warehouse'),
        ),
        migrations.AddField(
            model_name='stocktransfer',
            name='received_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transfers_received', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stocktransfer',
            name='shipped_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transfers_shipped', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stocktransfer',
            name='to_warehouse',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_in', to='inventory.warehouse'),
        ),
        migrations.CreateModel(
            name='StockTransaction',
            fields=[
                ('transaction_id', models.AutoField(primary_key=True, serialize=False)),
                ('transaction_type', models.CharField(choices=[('RECEIPT', 'Receipt'), ('ISSUE', 'Issue'), ('ADJUSTMENT', 'Adjustment'), ('TRANSFER_OUT', 'Transfer Out'), ('TRANSFER_IN', 'Transfer In'), ('RETURN', 'Return'), ('DAMAGE', 'Damage'), ('CYCLE_COUNT', 'Cycle Count')], max_length=20)),
                ('quantity', models.DecimalField(decimal_places=4, help_text='Positive for inbound, negative for outbound', max_digits=15)),
                ('unit_cost', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=15)),
                ('total_cost', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=15)),
                ('reference_type', models.CharField(blank=True, help_text='PO, SO, INV, ADJ, TRF', max_length=20)),
                ('reference_number', models.CharField(blank=True, max_length=100)),
                ('reference_id', models.PositiveIntegerField(blank=True, null=True)),
                ('description', models.TextField(blank=True)),
                ('batch_number', models.CharField(blank=True, max_length=50)),
                ('serial_number', models.CharField(blank=True, max_length=50)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('balance_quantity', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=15)),
                ('balance_value', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=15)),
                ('transaction_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='stock_transactions_created', to=settings.AUTH_USER_MODEL)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_transactions', to='Pricing.product')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_transactions', to='inventory.warehouse')),
            ],
            options={
                'db_table': 'stock_transactions',
                'ordering': ['-transaction_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockLevel',
            fields=[
                ('stock_level_id', models.AutoField(primary_key=True, serialize=False)),
                ('quantity_on_hand', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Physical quantity in warehouse', max_digits=15)),
                ('quantity_reserved', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Quantity reserved for sales orders', max_digits=15)),
                ('quantity_on_order', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Quantity on purchase orders', max_digits=15)),
                ('reorder_point', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Minimum stock level before reorder', max_digits=15)),
                ('reorder_quantity', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Standard reorder quantity', max_digits=15)),
                ('max_stock_level', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Maximum stock level', max_digits=15)),
                ('average_cost', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=15)),
                ('last_cost', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=15)),
                ('standard_cost', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=15)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('last_transaction_date', models.DateTimeField(blank=True, null=True)),
                ('last_counted_date', models.DateField(blank=True, help_text='Last physical count date', null=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_levels', to='Pricing.product')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_levels', to='inventory.warehouse')),
            ],
            options={
                'db_table': 'stock_levels',
                'ordering': ['product__code', 'warehouse__code'],
            },
        ),
        migrations.CreateModel(
            name='StockAdjustmentItem',
            fields=[
                ('item_id', models.AutoField(primary_key=True, serialize=False)),
                ('system_quantity', models.DecimalField(decimal_places=4, help_text='System quantity', max_digits=15)),
                ('physical_quantity', models.DecimalField(decimal_places=4, help_text='Physical count', max_digits=15)),
                ('adjustment_quantity', models.DecimalField(decimal_places=4, help_text='Difference', max_digits=15)),
                ('unit_cost', models.DecimalField(decimal_places=4, max_digits=15)),
                ('adjustment_value', models.DecimalField(decimal_places=2, max_digits=15)),
                ('notes', models.TextField(blank=True)),
                ('adjustment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.stockadjustment')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Pricing.product')),
            ],
            options={
                'db_table': 'stock_adjustment_items',
            },
        ),
        migrations.AddField(
            model_name='stockadjustment',
            name='warehouse',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='adjustments', to='inventory.warehouse'),
        ),
        migrations.AddIndex(
            model_name='warehouse',
            index=models.Index(fields=['code'], name='warehouses_code_339055_idx'),
        ),
        migrations.AddIndex(
            model_name='warehouse',
            index=models.Index(fields=['is_active'], name='warehouses_is_acti_178fdd_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='stockvaluationitem',
            unique_together={('valuation', 'product')},
        ),
        migrations.AlterUniqueTogether(
            name='stockvaluation',
            unique_together={('valuation_date', 'warehouse', 'method')},
        ),
        migrations.AlterUniqueTogether(
            name='stocktransferitem',
            unique_together={('transfer', 'product')},
        ),
        migrations.AddIndex(
            model_name='stocktransaction',
            index=models.Index(fields=['product', 'warehouse'], name='stock_trans_product_53e12a_idx'),
        ),
        migrations.AddIndex(
            model_name='stocktransaction',
            index=models.Index(fields=['transaction_type'], name='stock_trans_transac_b2b788_idx'),
        ),
        migrations.AddIndex(
            model_name='stocktransaction',
            index=models.Index(fields=['transaction_date'], name='stock_trans_transac_9087d2_idx'),
        ),
        migrations.AddIndex(
            model_name='stocktransaction',
            index=models.Index(fields=['reference_type', 'reference_id'], name='stock_trans_referen_883dfe_idx'),
        ),
        migrations.AddIndex(
            model_name='stocklevel',
            index=models.Index(fields=['product', 'warehouse'], name='stock_level_product_99778c_idx'),
        ),
        migrations.AddIndex(
            model_name='stocklevel',
            index=models.Index(fields=['quantity_on_hand'], name='stock_level_quantit_a53d04_idx'),
        ),
        migrations.AddIndex(
            model_name='stocklevel',
            index=models.Index(fields=['reorder_point'], name='stock_level_reorder_9567d2_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='stocklevel',
            unique_together={('product', 'warehouse')},
        ),
        migrations.AlterUniqueTogether(
            name='stockadjustmentitem',
            unique_together={('adjustment', 'product')},
        ),
    ]

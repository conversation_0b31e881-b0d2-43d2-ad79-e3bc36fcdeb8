from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid
from django.utils import timezone


class PaymentTerm(models.Model):
    """Payment terms that can be used across customers, invoices, etc."""
    
    name = models.CharField(max_length=100, unique=True, help_text="E.g., 'Net 30', 'Due on Receipt'")
    code = models.CharField(max_length=50, unique=True, help_text="E.g., 'net_30', 'due_on_receipt'")
    days = models.PositiveIntegerField(help_text="Number of days from invoice date")
    description = models.TextField(blank=True, null=True, help_text="Optional description")
    is_default = models.BooleanField(default=False, help_text="Is this the default payment term?")
    is_active = models.BooleanField(default=True, help_text="Is this payment term active?")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='payment_terms_created')
    
    class Meta:
        db_table = 'sales_payment_terms'
        ordering = ['days', 'name']
        verbose_name = 'Payment Term'
        verbose_name_plural = 'Payment Terms'
    
    def __str__(self):
        return f"{self.name} ({self.days} days)"
    
    def save(self, *args, **kwargs):
        # Ensure only one default payment term
        if self.is_default:
            PaymentTerm.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


# Customer model is now in contacts app - using contacts.Customer instead


class ProductCategory(models.Model):
    """Enhanced Product categories for retail inventory management"""
    
    DIVISION_TYPE_CHOICES = [
        ('perishable', 'Perishable'),
        ('refrigerated', 'Refrigerated'),
        ('frozen', 'Frozen'),
        ('controlled-substance', 'Controlled Substance'),
        ('non-perishable', 'Non-Perishable'),
    ]
    
    UNIT_OF_MEASURE_CHOICES = [
        ('piece', 'Piece'),
        ('kg', 'Kilogram'),
        ('gram', 'Gram'),
        ('liter', 'Liter'),
        ('ml', 'Milliliter'),
        ('box', 'Box'),
        ('pack', 'Pack'),
        ('dozen', 'Dozen'),
        ('meter', 'Meter'),
        ('cm', 'Centimeter'),
    ]
    
    # Basic Information
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=20, unique=True, default='CAT', help_text="Short code for the category (e.g., FRT, VEG)")
    description = models.TextField(blank=True, null=True)
    
    # Hierarchy
    parent_category = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True, related_name='subcategories')
    level = models.PositiveIntegerField(default=1, help_text="Category hierarchy level")
    
    # Division Type
    division_type = models.CharField(max_length=30, choices=DIVISION_TYPE_CHOICES, default='non-perishable')
    
    # Visual
    image_url = models.URLField(blank=True, null=True, help_text="Category image URL")
    
    # Business Settings
    tax_category = models.CharField(max_length=50, blank=True, null=True, help_text="Default tax category for products")
    margin_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.00, help_text="Default profit margin %")
    
    # Settings
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=1, help_text="Display order")
    allow_subcategories = models.BooleanField(default=True)
    requires_expiry_tracking = models.BooleanField(default=False)
    requires_batch_tracking = models.BooleanField(default=False)
    default_unit_of_measure = models.CharField(max_length=20, choices=UNIT_OF_MEASURE_CHOICES, default='piece')
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='categories_created')
    
    class Meta:
        db_table = 'sales_product_categories'
        verbose_name_plural = 'Product Categories'
        ordering = ['sort_order', 'name']
        unique_together = [['parent_category', 'name']]  # Allow same name in different parent categories
    
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    def save(self, *args, **kwargs):
        # Calculate level based on parent
        if self.parent_category:
            self.level = self.parent_category.level + 1
        else:
            self.level = 1
        
        # Validate level doesn't exceed 3
        if self.level > 3:
            raise ValueError("Category hierarchy cannot exceed 3 levels")
        
        super().save(*args, **kwargs)
    
    @property
    def full_path(self):
        """Get full category path (e.g., 'Food > Fruits > Citrus')"""
        if self.parent_category:
            return f"{self.parent_category.full_path} > {self.name}"
        return self.name
    
    @property
    def products_count(self):
        """Count of products in this category"""
        return self.products.filter(status='active').count()
    
    @property
    def subcategories_count(self):
        """Count of subcategories"""
        return self.subcategories.filter(is_active=True).count()
    
    def get_all_subcategories(self):
        """Get all subcategories recursively"""
        subcategories = list(self.subcategories.filter(is_active=True))
        for subcategory in self.subcategories.filter(is_active=True):
            subcategories.extend(subcategory.get_all_subcategories())
        return subcategories


class Product(models.Model):
    """Enhanced Product and Service model with Sales Price Authority"""
    
    PRODUCT_TYPE_CHOICES = [
        ('product', 'Product'),
        ('service', 'Service'),
        ('bundle', 'Bundle'),
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ]
    
    # Basic Information
    product_id = models.UUIDField(default=uuid.uuid4, unique=True)
    name = models.CharField(max_length=200)
    sku = models.CharField(max_length=100, unique=True, blank=True, null=True)
    product_type = models.CharField(max_length=20, choices=PRODUCT_TYPE_CHOICES, default='product')
    category = models.ForeignKey(ProductCategory, on_delete=models.SET_NULL, null=True, blank=True, related_name='products')
    description = models.TextField(blank=True, null=True)
    
    # Pricing - Enhanced with Sales Price Authority
    unit_price = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        default=0.00,
        help_text="Sales price (managed by Sales Department)"
    )
    cost_price = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        default=0.00, 
        blank=True, 
        null=True,
        help_text="Purchase cost (managed by Purchase Department)"
    )
    minimum_selling_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0.00,
        help_text="Minimum allowed selling price (for validation)"
    )
    
    # Price History and Authority
    price_effective_date = models.DateField(
        blank=True, 
        null=True,
        help_text="Date when current price became effective"
    )
    price_last_updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='products_priced',
        help_text="User who last updated the sales price"
    )
    price_last_updated_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When the price was last updated"
    )
    
    # GL Account Integration - PROPER FOREIGN KEY FIELDS
    income_account_gl = models.ForeignKey(
        'gl.Account',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='sales_products',
        limit_choices_to={'account_type__type': 'REVENUE'},
        help_text="Revenue account for sales of this product/service"
    )
    expense_account_gl = models.ForeignKey(
        'gl.Account', 
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='expense_products',
        limit_choices_to={'account_type__type': 'EXPENSE'},
        help_text="COGS/Expense account for purchasing this product"
    )
    inventory_asset_account_gl = models.ForeignKey(
        'gl.Account',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='inventory_products',
        limit_choices_to={'account_type__type': 'ASSET', 'detail_type__code': 'INVENTORY'},
        help_text="Asset account for inventory tracking"
    )
    sales_tax_category = models.CharField(max_length=100, blank=True, null=True)
    
    # Purchasing Information (for products)
    preferred_vendor = models.CharField(max_length=200, blank=True, null=True)
    
    # Inventory (for products only)
    track_inventory = models.BooleanField(default=False)
    reorder_point = models.IntegerField(default=0)
    quantity_on_hand = models.IntegerField(default=0)
    quantity_on_purchase_order = models.IntegerField(default=0)
    quantity_on_sales_order = models.IntegerField(default=0)
    
    # Settings
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='active')
    taxable = models.BooleanField(default=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='products_created')
    
    class Meta:
        db_table = 'sales_products'
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        # Track price updates
        if self.pk:  # Existing product
            old_product = Product.objects.get(pk=self.pk)
            if old_product.unit_price != self.unit_price:
                self.price_last_updated_at = timezone.now()
                # price_last_updated_by should be set by the view
        super().save(*args, **kwargs)
    
    @property
    def margin_amount(self):
        """Calculate margin amount (sales price - cost price)"""
        if self.cost_price and self.unit_price:
            return self.unit_price - self.cost_price
        return Decimal('0.00')
    
    @property
    def margin_percentage(self):
        """Calculate margin percentage"""
        if self.cost_price and self.cost_price > 0 and self.unit_price:
            return ((self.unit_price - self.cost_price) / self.cost_price) * 100
        return Decimal('0.00')
    
    @property
    def markup_percentage(self):
        """Calculate markup percentage (margin / cost price)"""
        if self.cost_price and self.cost_price > 0:
            return (self.margin_amount / self.cost_price) * 100
        return Decimal('0.00')
    
    def get_current_average_cost(self):
        """Get current weighted average cost from inventory"""
        try:
            from inventory.models import Inventory
            inventory_items = Inventory.objects.filter(product=self)
            total_qty = sum(item.quantity_on_hand for item in inventory_items)
            total_value = sum(item.quantity_on_hand * item.average_cost for item in inventory_items)
            
            if total_qty > 0:
                return total_value / total_qty
            return self.cost_price or Decimal('0.00')
        except:
            return self.cost_price or Decimal('0.00')
    
    def get_sales_account(self):
        """Get the GL account for sales revenue"""
        return self.income_account_gl
    
    def get_cogs_account(self):
        """Get the GL account for cost of goods sold"""
        return self.expense_account_gl
    
    def get_inventory_account(self):
        """Get the GL account for inventory asset"""
        return self.inventory_asset_account_gl
    
    def create_sale_journal_entry(self, quantity, sale_price, customer=None, warehouse=None):
        """Create GL journal entry for a sale of this product with proper COGS"""
        if not self.income_account_gl:
            raise ValueError(f"No income account configured for product {self.name}")
        
        from decimal import Decimal
        from django.utils import timezone
        
        total_revenue = Decimal(str(quantity)) * Decimal(str(sale_price))
        
        # Get COGS from weighted average cost
        cogs_per_unit = self.get_current_average_cost()
        total_cogs = Decimal(str(quantity)) * cogs_per_unit
        
        # Prepare journal entry data
        journal_entry_data = {
            'entry_number': f"SALE-{self.sku}-{timezone.now().strftime('%Y%m%d%H%M%S')}",
            'description': f"Sale of {self.name}",
            'transaction_date': timezone.now().date(),
            'source_document_type': 'SALE',
            'source_document_id': f"SALE-{self.id}",
            'net_amount': total_revenue,
            'lines': []
        }
        
        # Revenue entry: Dr. Accounts Receivable, Cr. Sales Revenue
        journal_entry_data['lines'].extend([
            {
                'account': 'accounts_receivable',  # Will be resolved to proper account
                'description': f"Sale to {customer.display_name if customer else 'Customer'} - {self.name}",
                'debit_amount': total_revenue,
                'credit_amount': Decimal('0.00'),
                'customer': customer.id if customer else None,
                'product': self.id,
                    'quantity': quantity,
                    'unit_price': sale_price
                },
                {
                'account': self.income_account_gl.id,
                'description': f"Sales Revenue - {self.name}",
                'debit_amount': Decimal('0.00'),
                'credit_amount': total_revenue,
                'customer': customer.id if customer else None,
                'product': self.id,
                    'quantity': quantity,
                    'unit_price': sale_price
                }
        ])
        
        # COGS entry (if inventory product): Dr. COGS, Cr. Inventory Asset
        if self.track_inventory and self.expense_account_gl and self.inventory_asset_account_gl:
            journal_entry_data['lines'].extend([
                {
                    'account': self.expense_account_gl.id,
                    'description': f"COGS - {self.name}",
                    'debit_amount': total_cogs,
                    'credit_amount': Decimal('0.00'),
                    'product': self.id,
                    'quantity': quantity,
                    'unit_price': cogs_per_unit
                },
                {
                    'account': self.inventory_asset_account_gl.id,
                    'description': f"Inventory reduction - {self.name}",
                    'debit_amount': Decimal('0.00'),
                    'credit_amount': total_cogs,
                    'product': self.id,
                    'quantity': quantity,
                    'unit_price': cogs_per_unit
                }
            ])
        
        return journal_entry_data


# New Invoice System - Built from scratch following QuickBooks/ERP standards

class Invoice(models.Model):
    """
    Customer Invoice model - Built from scratch following QuickBooks/ERP standards
    Comprehensive invoice management with proper GL integration
    """

    # Invoice Status Choices
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('sent', 'Sent'),
        ('paid', 'Paid'),
        ('overdue', 'Overdue'),
        ('cancelled', 'Cancelled'),
        ('partial', 'Partially Paid'),
    ]

    # Core Invoice Header Fields
    invoice_id = models.UUIDField(default=uuid.uuid4, unique=True, help_text="Unique invoice identifier")
    invoice_number = models.CharField(
        max_length=50,
        unique=True,
        help_text="Auto-generated unique invoice number (e.g., INV-2023-1001)"
    )

    # Customer Information (Required)
    customer = models.ForeignKey(
        'contacts.Contact',
        on_delete=models.PROTECT,
        related_name='invoices',
        help_text="Customer from contacts system"
    )

    # Date Fields (Required)
    invoice_date = models.DateField(
        help_text="Invoice issue date"
    )
    due_date = models.DateField(
        help_text="Payment deadline date"
    )

    # Status and Terms (Required)
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        help_text="Current invoice status"
    )
    payment_terms = models.ForeignKey(
        PaymentTerm,
        on_delete=models.PROTECT,
        help_text="Payment terms (references existing PaymentTerm table)"
    )

    # Optional Reference Fields
    po_number = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Customer's Purchase Order reference number"
    )
    sales_rep = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='invoices_as_sales_rep',
        help_text="Responsible sales representative"
    )

    # Financial Fields - Following your database schema
    subtotal = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Sum before tax/discount"
    )
    tax_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Calculated tax total"
    )
    discount_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Global discount amount"
    )
    shipping_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Shipping costs"
    )
    total_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Final amount (subtotal + tax + shipping - discount)"
    )
    amount_paid = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Tracks partial payments"
    )
    balance_due = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="total_amount - amount_paid"
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='invoices_created',
        help_text="User who created the invoice"
    )

    class Meta:
        db_table = 'sales_invoices'
        ordering = ['-invoice_date', '-created_at']
        verbose_name = 'Invoice'
        verbose_name_plural = 'Invoices'
        indexes = [
            models.Index(fields=['invoice_number']),
            models.Index(fields=['customer', 'invoice_date']),
            models.Index(fields=['status', 'due_date']),
        ]

    def __str__(self):
        return f"{self.invoice_number} - {self.customer.name} - ${self.total_amount}"

    def save(self, *args, **kwargs):
        # Auto-generate invoice number if not provided
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()

        # Auto-calculate due date based on payment terms if not provided
        if not self.due_date and self.invoice_date and self.payment_terms:
            from datetime import timedelta
            self.due_date = self.invoice_date + timedelta(days=self.payment_terms.days)

        super().save(*args, **kwargs)

    def generate_invoice_number(self):
        """Generate unique invoice number in format INV-YYYY-NNNN"""
        from datetime import datetime
        year = datetime.now().year

        # Get the last invoice number for this year
        last_invoice = Invoice.objects.filter(
            invoice_number__startswith=f'INV-{year}-'
        ).order_by('-invoice_number').first()

        if last_invoice:
            # Extract the sequence number and increment
            try:
                last_seq = int(last_invoice.invoice_number.split('-')[-1])
                next_seq = last_seq + 1
            except (ValueError, IndexError):
                next_seq = 1
        else:
            next_seq = 1

        return f'INV-{year}-{next_seq:04d}'

    def calculate_totals(self):
        """Calculate all financial totals from line items"""
        line_items = self.line_items.all()

        # Calculate subtotal from line items
        subtotal = sum(item.line_total for item in line_items)

        # Calculate tax amount from line items
        tax_amount = sum(item.tax_amount for item in line_items)

        # Calculate total amount
        total_amount = subtotal + tax_amount + self.shipping_amount - self.discount_amount

        # Calculate balance due
        balance_due = total_amount - self.amount_paid

        # Update fields
        self.subtotal = subtotal
        self.tax_amount = tax_amount
        self.total_amount = total_amount
        self.balance_due = balance_due

        return {
            'subtotal': subtotal,
            'tax_amount': tax_amount,
            'total_amount': total_amount,
            'balance_due': balance_due
        }

    def recalculate_and_save(self):
        """Recalculate totals and save the invoice"""
        self.calculate_totals()
        self.save()

    @property
    def is_overdue(self):
        """Check if invoice is overdue"""
        from django.utils import timezone
        return (
            self.status in ['sent', 'partial'] and
            self.due_date < timezone.now().date()
        )

    @property
    def days_overdue(self):
        """Calculate days overdue"""
        if self.is_overdue:
            from django.utils import timezone
            return (timezone.now().date() - self.due_date).days
        return 0


class InvoiceLineItem(models.Model):
    """
    Invoice Line Items - Following your database schema
    Links to Pricing.Product for auto-price filling
    """

    # Core Fields
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        related_name='line_items',
        help_text="Parent invoice"
    )
    product = models.ForeignKey(
        'Pricing.Product',
        on_delete=models.PROTECT,
        help_text="Product from Pricing module"
    )
    description = models.TextField(
        blank=True,
        null=True,
        help_text="Overrides product description if provided"
    )

    # Quantity and Pricing
    quantity = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Units sold (supports fractional qty)"
    )
    unit_price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="Price per unit (auto-filled from pricing module)"
    )

    # Tax Information
    taxable = models.BooleanField(
        default=True,
        help_text="Whether tax applies to this line item"
    )
    tax_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Tax rate percentage (overrides default if provided)"
    )

    # Calculated Fields
    line_total = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="quantity * unit_price (calculated automatically)"
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'invoice_line_items'
        ordering = ['id']
        verbose_name = 'Invoice Line Item'
        verbose_name_plural = 'Invoice Line Items'

    def __str__(self):
        return f"{self.invoice.invoice_number} - {self.product.code} x {self.quantity}"

    def save(self, *args, **kwargs):
        # Auto-fill unit price from pricing module if not provided
        if not self.unit_price:
            self.unit_price = self.get_product_price()

        # Auto-fill description from product if not provided
        if not self.description:
            self.description = self.product.description or self.product.name

        # Auto-fill tax rate from product if not provided
        if not self.tax_rate and self.product.tax_code:
            self.tax_rate = self.product.tax_code.rate

        # Calculate line total
        self.line_total = self.quantity * self.unit_price

        super().save(*args, **kwargs)

        # Recalculate invoice totals after saving line item
        self.invoice.recalculate_and_save()

    def delete(self, *args, **kwargs):
        invoice = self.invoice
        super().delete(*args, **kwargs)
        # Recalculate invoice totals after deleting line item
        invoice.recalculate_and_save()

    def get_product_price(self):
        """
        Get product price from Pricing module
        Priority: Customer-specific price list > Default price list > Product standard cost
        """
        try:
            # Try to get customer-specific price list
            from Pricing.models import CustomerPriceList, PriceListItem, PriceList

            customer_price_lists = CustomerPriceList.objects.filter(
                customer=self.invoice.customer.customer,  # Access Customer model through Contact
                price_list__is_active=True,
                effective_date__lte=self.invoice.invoice_date
            ).filter(
                models.Q(expiry_date__isnull=True) | models.Q(expiry_date__gte=self.invoice.invoice_date)
            )

            if customer_price_lists.exists():
                # Get price from customer's price list
                price_list = customer_price_lists.first().price_list
                price_item = PriceListItem.objects.filter(
                    price_list=price_list,
                    product=self.product,
                    min_quantity__lte=self.quantity,
                    effective_date__lte=self.invoice.invoice_date
                ).filter(
                    models.Q(expiry_date__isnull=True) | models.Q(expiry_date__gte=self.invoice.invoice_date)
                ).order_by('-min_quantity').first()

                if price_item:
                    return price_item.unit_price

            # Try default price list
            default_price_list = PriceList.objects.filter(is_default=True, is_active=True).first()
            if default_price_list:
                price_item = PriceListItem.objects.filter(
                    price_list=default_price_list,
                    product=self.product,
                    min_quantity__lte=self.quantity,
                    effective_date__lte=self.invoice.invoice_date
                ).filter(
                    models.Q(expiry_date__isnull=True) | models.Q(expiry_date__gte=self.invoice.invoice_date)
                ).order_by('-min_quantity').first()

                if price_item:
                    return price_item.unit_price

            # Fallback to product standard cost
            return self.product.standard_cost

        except Exception as e:
            # Fallback to product standard cost if any error occurs
            return getattr(self.product, 'standard_cost', Decimal('0.00'))

    @property
    def tax_amount(self):
        """Calculate tax amount for this line item"""
        if self.taxable and self.tax_rate:
            return (self.line_total * self.tax_rate / 100).quantize(Decimal('0.01'))
        return Decimal('0.00')

    @property
    def total_with_tax(self):
        """Calculate line total including tax"""
        return self.line_total + self.tax_amount


class Payment(models.Model):
    """Payment model for tracking invoice payments"""
    
    PAYMENT_METHOD_CHOICES = [
        ('cash', 'Cash'),
        ('check', 'Check'),
        ('credit_card', 'Credit Card'),
        ('bank_transfer', 'Bank Transfer'),
        ('online', 'Online Payment'),
        ('other', 'Other'),
    ]
    
    payment_id = models.UUIDField(default=uuid.uuid4, unique=True)
    customer = models.ForeignKey('contacts.Contact', on_delete=models.SET_NULL, null=True, blank=True, related_name='payments', help_text='Customer from contacts system')
    
    # Payment details
    payment_date = models.DateField()
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, default='cash')
    reference_number = models.CharField(max_length=100, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    
    # Deposit information
    deposit_to_account = models.CharField(max_length=200, blank=True, null=True)  # Link to GL account
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='payments_created')
    
    class Meta:
        db_table = 'sales_payments'
        ordering = ['-payment_date']
    
    def __str__(self):
        return f"Payment {self.payment_id} - {self.customer.display_name} - {self.amount}"


# Estimate and EstimateLineItem models removed - will be replaced with new system

# Generated by Django 4.2.21 on 2025-06-29 08:54

from decimal import Decimal
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('Pricing', '0002_alter_pricelistitem_product'),
        ('sales', '0014_invoice'),
    ]

    operations = [
        migrations.AddField(
            model_name='invoice',
            name='amount_paid',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Tracks partial payments', max_digits=12),
        ),
        migrations.AddField(
            model_name='invoice',
            name='balance_due',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='total_amount - amount_paid', max_digits=12),
        ),
        migrations.AddField(
            model_name='invoice',
            name='discount_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Global discount amount', max_digits=12),
        ),
        migrations.AddField(
            model_name='invoice',
            name='shipping_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Shipping costs', max_digits=12),
        ),
        migrations.AddField(
            model_name='invoice',
            name='subtotal',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Sum before tax/discount', max_digits=12),
        ),
        migrations.AddField(
            model_name='invoice',
            name='tax_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Calculated tax total', max_digits=12),
        ),
        migrations.AddField(
            model_name='invoice',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Final amount (subtotal + tax + shipping - discount)', max_digits=12),
        ),
        migrations.CreateModel(
            name='InvoiceLineItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(blank=True, help_text='Overrides product description if provided', null=True)),
                ('quantity', models.DecimalField(decimal_places=2, help_text='Units sold (supports fractional qty)', max_digits=10)),
                ('unit_price', models.DecimalField(decimal_places=2, help_text='Price per unit (auto-filled from pricing module)', max_digits=12)),
                ('taxable', models.BooleanField(default=True, help_text='Whether tax applies to this line item')),
                ('tax_rate', models.DecimalField(blank=True, decimal_places=2, help_text='Tax rate percentage (overrides default if provided)', max_digits=5, null=True)),
                ('line_total', models.DecimalField(decimal_places=2, help_text='quantity * unit_price (calculated automatically)', max_digits=12)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('invoice', models.ForeignKey(help_text='Parent invoice', on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='sales.invoice')),
                ('product', models.ForeignKey(help_text='Product from Pricing module', on_delete=django.db.models.deletion.PROTECT, to='Pricing.product')),
            ],
            options={
                'verbose_name': 'Invoice Line Item',
                'verbose_name_plural': 'Invoice Line Items',
                'db_table': 'invoice_line_items',
                'ordering': ['id'],
            },
        ),
    ]

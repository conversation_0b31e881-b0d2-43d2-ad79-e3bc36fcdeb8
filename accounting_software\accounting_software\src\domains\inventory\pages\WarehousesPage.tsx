/**
 * Warehouses Page
 * Multi-location warehouse management
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Input, 
  Select, 
  Tag, 
  Modal,
  Form,
  Row,
  Col,
  Alert,
  Spin,
  Tooltip,
  Switch,
  message
} from 'antd';
import { 
  SearchOutlined, 
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ShopOutlined,
  EnvironmentOutlined,
  PhoneOutlined,
  MailOutlined
} from '@ant-design/icons';
import { inventoryService, Warehouse } from '../services/inventory.service';

const { Option } = Select;

const WarehousesPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedType, setSelectedType] = useState<string | undefined>();
  
  // Modal states
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingWarehouse, setEditingWarehouse] = useState<Warehouse | null>(null);
  const [modalLoading, setModalLoading] = useState(false);
  
  const [form] = Form.useForm();

  useEffect(() => {
    loadWarehouses();
  }, []);

  const loadWarehouses = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await inventoryService.getWarehouses();
      setWarehouses(data);
    } catch (err) {
      console.error('❌ Error loading warehouses:', err);
      setError('Failed to load warehouses. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingWarehouse(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (warehouse: Warehouse) => {
    setEditingWarehouse(warehouse);
    form.setFieldsValue(warehouse);
    setIsModalVisible(true);
  };

  const handleDelete = (warehouse: Warehouse) => {
    Modal.confirm({
      title: 'Delete Warehouse',
      content: `Are you sure you want to delete warehouse "${warehouse.code} - ${warehouse.name}"?`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          await inventoryService.deleteWarehouse(warehouse.warehouse_id);
          message.success('Warehouse deleted successfully');
          loadWarehouses();
        } catch (err) {
          console.error('❌ Error deleting warehouse:', err);
          message.error('Failed to delete warehouse');
        }
      },
    });
  };

  const handleSubmit = async (values: any) => {
    try {
      setModalLoading(true);
      
      if (editingWarehouse) {
        await inventoryService.updateWarehouse(editingWarehouse.warehouse_id, values);
        message.success('Warehouse updated successfully');
      } else {
        await inventoryService.createWarehouse(values);
        message.success('Warehouse created successfully');
      }
      
      setIsModalVisible(false);
      loadWarehouses();
    } catch (err) {
      console.error('❌ Error saving warehouse:', err);
      message.error('Failed to save warehouse');
    } finally {
      setModalLoading(false);
    }
  };

  const getWarehouseTypeColor = (type: string) => {
    switch (type) {
      case 'MAIN': return 'blue';
      case 'BRANCH': return 'green';
      case 'VIRTUAL': return 'purple';
      case 'DAMAGED': return 'red';
      default: return 'default';
    }
  };

  const getWarehouseTypeIcon = (type: string) => {
    switch (type) {
      case 'MAIN': return <ShopOutlined />;
      case 'BRANCH': return <EnvironmentOutlined />;
      case 'VIRTUAL': return <ShopOutlined />;
      case 'DAMAGED': return <ShopOutlined />;
      default: return <ShopOutlined />;
    }
  };

  const filteredWarehouses = warehouses.filter(warehouse => {
    const matchesSearch = !searchText || 
      warehouse.code.toLowerCase().includes(searchText.toLowerCase()) ||
      warehouse.name.toLowerCase().includes(searchText.toLowerCase()) ||
      warehouse.city?.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesType = !selectedType || warehouse.warehouse_type === selectedType;
    
    return matchesSearch && matchesType;
  });

  const columns = [
    {
      title: 'Code',
      dataIndex: 'code',
      key: 'code',
      width: 100,
      render: (code: string) => (
        <div className="font-medium">{code}</div>
      ),
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: 'Type',
      dataIndex: 'warehouse_type',
      key: 'warehouse_type',
      width: 120,
      render: (type: string) => (
        <Tag 
          color={getWarehouseTypeColor(type)} 
          icon={getWarehouseTypeIcon(type)}
        >
          {type}
        </Tag>
      ),
    },
    {
      title: 'Location',
      key: 'location',
      width: 200,
      render: (_: any, record: Warehouse) => (
        <div>
          {record.address && <div className="text-sm">{record.address}</div>}
          <div className="text-sm text-gray-500">
            {[record.city, record.state, record.postal_code].filter(Boolean).join(', ')}
          </div>
        </div>
      ),
    },
    {
      title: 'Contact',
      key: 'contact',
      width: 150,
      render: (_: any, record: Warehouse) => (
        <div className="space-y-1">
          {record.phone && (
            <div className="text-sm flex items-center">
              <PhoneOutlined className="mr-1" />
              {record.phone}
            </div>
          )}
          {record.email && (
            <div className="text-sm flex items-center">
              <MailOutlined className="mr-1" />
              {record.email}
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Settings',
      key: 'settings',
      width: 150,
      render: (_: any, record: Warehouse) => (
        <div className="space-y-1">
          {record.is_default && (
            <Tag color="gold">Default</Tag>
          )}
          {record.allow_negative_stock && (
            <Tag color="orange">Negative Stock</Tag>
          )}
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_: any, record: Warehouse) => (
        <Space>
          <Tooltip title="Edit">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined />} 
              onClick={() => handleDelete(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  if (error) {
    return (
      <Alert
        message="Error"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={loadWarehouses}>
            Retry
          </Button>
        }
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Warehouses</h1>
          <p className="text-gray-600">Manage warehouse locations and settings</p>
        </div>
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={handleCreate}
        >
          New Warehouse
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12}>
            <Input
              placeholder="Search warehouses..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col xs={24} sm={8}>
            <Select
              placeholder="Filter by type"
              value={selectedType}
              onChange={setSelectedType}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="MAIN">Main Warehouse</Option>
              <Option value="BRANCH">Branch Location</Option>
              <Option value="VIRTUAL">Virtual/Consignment</Option>
              <Option value="DAMAGED">Damaged Goods</Option>
            </Select>
          </Col>
          <Col xs={24} sm={4}>
            <Button 
              onClick={() => {
                setSearchText('');
                setSelectedType(undefined);
              }}
            >
              Clear Filters
            </Button>
          </Col>
        </Row>
      </Card>

      {/* Warehouses Table */}
      <Card>
        <Table
          dataSource={filteredWarehouses}
          columns={columns}
          rowKey="warehouse_id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} of ${total} warehouses`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Create/Edit Modal */}
      <Modal
        title={editingWarehouse ? 'Edit Warehouse' : 'Create Warehouse'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            warehouse_type: 'BRANCH',
            country: 'USA',
            is_active: true,
            allow_negative_stock: false,
            is_default: false,
          }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="code"
                label="Warehouse Code"
                rules={[{ required: true, message: 'Please enter warehouse code' }]}
              >
                <Input placeholder="e.g., MAIN, BR001" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="name"
                label="Warehouse Name"
                rules={[{ required: true, message: 'Please enter warehouse name' }]}
              >
                <Input placeholder="e.g., Main Warehouse" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="warehouse_type"
                label="Warehouse Type"
                rules={[{ required: true, message: 'Please select warehouse type' }]}
              >
                <Select>
                  <Option value="MAIN">Main Warehouse</Option>
                  <Option value="BRANCH">Branch Location</Option>
                  <Option value="VIRTUAL">Virtual/Consignment</Option>
                  <Option value="DAMAGED">Damaged Goods</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="manager" label="Manager">
                <Input placeholder="Manager name or ID" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="address" label="Address">
            <Input.TextArea rows={2} placeholder="Street address" />
          </Form.Item>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8}>
              <Form.Item name="city" label="City">
                <Input placeholder="City" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item name="state" label="State">
                <Input placeholder="State" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item name="postal_code" label="Postal Code">
                <Input placeholder="Postal code" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8}>
              <Form.Item name="country" label="Country">
                <Input placeholder="Country" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item name="phone" label="Phone">
                <Input placeholder="Phone number" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item name="email" label="Email">
                <Input type="email" placeholder="Email address" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8}>
              <Form.Item name="is_active" label="Active" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item name="allow_negative_stock" label="Allow Negative Stock" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item name="is_default" label="Default Warehouse" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <div className="flex justify-end space-x-2 mt-6">
            <Button onClick={() => setIsModalVisible(false)}>
              Cancel
            </Button>
            <Button type="primary" htmlType="submit" loading={modalLoading}>
              {editingWarehouse ? 'Update' : 'Create'}
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default WarehousesPage;

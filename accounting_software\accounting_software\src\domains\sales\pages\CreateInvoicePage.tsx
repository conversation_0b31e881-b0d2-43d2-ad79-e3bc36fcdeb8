// Create Invoice Page - New invoice creation
// Built from scratch based on backend functionality

import React from 'react';
import { Box } from '@mui/material';
import { PageContainer } from '../../../layouts/components/PageComponents';
import InvoiceForm from '../components/InvoiceForm';

const CreateInvoicePage: React.FC = () => {
  return (
    <PageContainer>
      <Box sx={{ maxWidth: '100%', mx: 'auto' }}>
        <InvoiceForm />
      </Box>
    </PageContainer>
  );
};

export default CreateInvoicePage;

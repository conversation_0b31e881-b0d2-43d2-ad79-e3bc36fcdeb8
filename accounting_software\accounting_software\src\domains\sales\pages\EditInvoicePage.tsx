// Edit Invoice Page - Edit existing invoice
// Built from scratch based on backend functionality

import React from 'react';
import { Box } from '@mui/material';
import { useParams } from 'react-router-dom';
import { PageContainer } from '../../../layouts/components/PageComponents';
import InvoiceForm from '../components/InvoiceForm';

const EditInvoicePage: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <PageContainer>
      <Box sx={{ maxWidth: '100%', mx: 'auto' }}>
        <InvoiceForm invoiceId={id} />
      </Box>
    </PageContainer>
  );
};

export default EditInvoicePage;

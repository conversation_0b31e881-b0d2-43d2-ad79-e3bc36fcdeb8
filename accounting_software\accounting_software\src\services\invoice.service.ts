// Invoice Service - API calls for Invoice management
// Based on Django backend endpoints

import api from './api';
import {
  Invoice,
  InvoiceFormData,
  InvoiceListResponse,
  InvoiceCreateResponse,
  InvoiceUpdateResponse,
  InvoiceDeleteResponse,
  InvoiceFilters,
  InvoiceStats,
  CustomerOption,
  ProductOption,
  PaymentTermOption,
  SalesRepOption,
  InvoiceInventorySummary,
  InvoiceInventoryReport,
} from '../shared/types/invoice.types';

const API_BASE = '/api/sales';

class InvoiceService {
  // Invoice CRUD Operations
  async getInvoices(filters?: InvoiceFilters): Promise<InvoiceListResponse> {
    const response = await api.get(`${API_BASE}/invoices/`, { params: filters });
    return response.data;
  }

  async getInvoice(id: number): Promise<Invoice> {
    const response = await api.get(`${API_BASE}/invoices/${id}/`);
    return response.data;
  }

  async createInvoice(data: InvoiceFormData): Promise<InvoiceCreateResponse> {
    const response = await api.post(`${API_BASE}/invoices/`, data);
    return response.data;
  }

  async updateInvoice(id: number, data: Partial<InvoiceFormData>): Promise<InvoiceUpdateResponse> {
    const response = await api.put(`${API_BASE}/invoices/${id}/`, data);
    return response.data;
  }

  async deleteInvoice(id: number): Promise<InvoiceDeleteResponse> {
    const response = await api.delete(`${API_BASE}/invoices/${id}/`);
    return response.data;
  }

  // Invoice Status Operations
  async updateInvoiceStatus(id: number, status: string): Promise<InvoiceUpdateResponse> {
    const response = await api.patch(`${API_BASE}/invoices/${id}/`, { status });
    return response.data;
  }

  async sendInvoice(id: number): Promise<InvoiceUpdateResponse> {
    const response = await api.post(`${API_BASE}/invoices/${id}/send/`);
    return response.data;
  }

  async cancelInvoice(id: number): Promise<InvoiceUpdateResponse> {
    const response = await api.post(`${API_BASE}/invoices/${id}/cancel/`);
    return response.data;
  }

  // Inventory Operations
  async checkInventoryAvailability(id: number, warehouse_id?: number): Promise<{
    all_available: boolean;
    inventory_report: InvoiceInventoryReport[];
  }> {
    const params = warehouse_id ? { warehouse_id } : {};
    const response = await api.get(`${API_BASE}/invoices/${id}/check-inventory/`, { params });
    return response.data;
  }

  async issueInventory(id: number, warehouse_id?: number): Promise<{
    success: boolean;
    results: any;
  }> {
    const data = warehouse_id ? { warehouse_id } : {};
    const response = await api.post(`${API_BASE}/invoices/${id}/issue-inventory/`, data);
    return response.data;
  }

  async getInventorySummary(id: number, warehouse_id?: number): Promise<InvoiceInventorySummary> {
    const params = warehouse_id ? { warehouse_id } : {};
    const response = await api.get(`${API_BASE}/invoices/${id}/inventory-summary/`, { params });
    return response.data;
  }

  // Statistics and Reports
  async getInvoiceStats(): Promise<InvoiceStats> {
    const response = await api.get(`${API_BASE}/invoices/stats/`);
    return response.data;
  }

  // Lookup Data for Forms
  async getCustomers(search?: string): Promise<CustomerOption[]> {
    const params = search ? { search } : {};
    const response = await api.get('/api/contacts/customers/', { params });
    return response.data.results || response.data;
  }

  async getProducts(search?: string): Promise<ProductOption[]> {
    const params = search ? { search } : {};
    const response = await api.get('/api/pricing/products/', { params });
    return response.data.results || response.data;
  }

  async getPaymentTerms(): Promise<PaymentTermOption[]> {
    const response = await api.get(`${API_BASE}/payment-terms/`);
    return response.data.results || response.data;
  }

  async getSalesReps(): Promise<SalesRepOption[]> {
    const response = await api.get('/api/auth/users/', { params: { is_active: true } });
    return response.data.results || response.data;
  }

  // Product Price Lookup
  async getProductPrice(productId: number, customerId?: number): Promise<{
    sale_price: string;
    cost_price: string;
    tax_rate?: string;
  }> {
    const params = customerId ? { customer_id: customerId } : {};
    const response = await api.get(`/api/pricing/products/${productId}/price/`, { params });
    return response.data;
  }

  // Product Inventory Check
  async checkProductInventory(productId: number, quantity: string, warehouse_id?: number): Promise<{
    is_available: boolean;
    available_quantity: string;
    shortage: string;
    warehouse_info: any;
  }> {
    const params = {
      quantity,
      ...(warehouse_id && { warehouse_id })
    };
    const response = await api.get(`/api/inventory/products/${productId}/check-availability/`, { params });
    return response.data;
  }

  // Invoice PDF Generation
  async generateInvoicePDF(id: number): Promise<Blob> {
    const response = await api.get(`${API_BASE}/invoices/${id}/pdf/`, {
      responseType: 'blob'
    });
    return response.data;
  }

  // Invoice Email
  async emailInvoice(id: number, email: string, message?: string): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await api.post(`${API_BASE}/invoices/${id}/email/`, {
      email,
      message
    });
    return response.data;
  }

  // Duplicate Invoice
  async duplicateInvoice(id: number): Promise<InvoiceCreateResponse> {
    const response = await api.post(`${API_BASE}/invoices/${id}/duplicate/`);
    return response.data;
  }

  // Invoice Number Generation (for preview)
  async getNextInvoiceNumber(): Promise<{ invoice_number: string }> {
    const response = await api.get(`${API_BASE}/invoices/next-number/`);
    return response.data;
  }

  // Batch Operations
  async bulkUpdateStatus(ids: number[], status: string): Promise<{
    success: boolean;
    updated_count: number;
    message: string;
  }> {
    const response = await api.post(`${API_BASE}/invoices/bulk-update-status/`, {
      ids,
      status
    });
    return response.data;
  }

  async bulkDelete(ids: number[]): Promise<{
    success: boolean;
    deleted_count: number;
    message: string;
  }> {
    const response = await api.post(`${API_BASE}/invoices/bulk-delete/`, { ids });
    return response.data;
  }

  // Search and Filters
  async searchInvoices(query: string): Promise<Invoice[]> {
    const response = await api.get(`${API_BASE}/invoices/search/`, {
      params: { q: query }
    });
    return response.data.results || response.data;
  }

  // Recent Invoices
  async getRecentInvoices(limit: number = 10): Promise<Invoice[]> {
    const response = await api.get(`${API_BASE}/invoices/recent/`, {
      params: { limit }
    });
    return response.data.results || response.data;
  }

  // Customer Invoice History
  async getCustomerInvoices(customerId: number, limit?: number): Promise<Invoice[]> {
    const params = limit ? { limit } : {};
    const response = await api.get(`${API_BASE}/customers/${customerId}/invoices/`, { params });
    return response.data.results || response.data;
  }
}

export const invoiceService = new InvoiceService();
export default invoiceService;

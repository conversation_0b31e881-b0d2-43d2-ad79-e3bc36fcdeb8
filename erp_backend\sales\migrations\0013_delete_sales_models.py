# Generated manually to delete sales models
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('sales', '0012_remove_invoicelineitem_invoice_and_more'),
    ]

    operations = [
        # Delete line item tables first (due to foreign key dependencies)
        migrations.DeleteModel(
            name='DeliveryNoteItem',
        ),
        migrations.DeleteModel(
            name='SalesOrderLineItem',
        ),
        migrations.DeleteModel(
            name='EstimateLineItem',
        ),
        
        # Delete main tables
        migrations.DeleteModel(
            name='DeliveryNote',
        ),
        migrations.DeleteModel(
            name='SalesOrder',
        ),
        migrations.DeleteModel(
            name='Estimate',
        ),
    ]

/**
 * Stock Transactions Page
 * Complete audit trail of all inventory movements
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Input, 
  Select, 
  Tag, 
  Row, 
  Col, 
  DatePicker,
  Alert,
  Spin,
  Tooltip,
  Statistic
} from 'antd';
import { 
  SearchOutlined, 
  ReloadOutlined, 
  FilterOutlined,
  ExportOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  SwapOutlined,
  EditOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import { inventoryService, StockTransaction, Warehouse } from '../services/inventory.service';

const { Option } = Select;
const { RangePicker } = DatePicker;

const StockTransactionsPage: React.FC = () => {
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(true);
  const [transactions, setTransactions] = useState<StockTransaction[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [error, setError] = useState<string | null>(null);

  // Filters
  const [searchText, setSearchText] = useState('');
  const [selectedWarehouse, setSelectedWarehouse] = useState<number | undefined>();
  const [selectedTransactionType, setSelectedTransactionType] = useState<string | undefined>();
  const [selectedReferenceType, setSelectedReferenceType] = useState<string | undefined>();
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);

  useEffect(() => {
    loadWarehouses();
    loadTransactions();
  }, []);

  useEffect(() => {
    loadTransactions();
  }, [currentPage, pageSize, searchText, selectedWarehouse, selectedTransactionType, selectedReferenceType, dateRange]);

  const loadWarehouses = async () => {
    try {
      const data = await inventoryService.getWarehouses();
      setWarehouses(data);
    } catch (err) {
      console.error('❌ Error loading warehouses:', err);
    }
  };

  const loadTransactions = async () => {
    try {
      setLoading(true);
      setError(null);

      const params: any = {
        page: currentPage,
        page_size: pageSize,
      };

      if (searchText) params.search = searchText;
      if (selectedWarehouse) params.warehouse = selectedWarehouse;
      if (selectedTransactionType) params.transaction_type = selectedTransactionType;
      if (selectedReferenceType) params.reference_type = selectedReferenceType;
      if (dateRange) {
        params.start_date = dateRange[0].format('YYYY-MM-DD');
        params.end_date = dateRange[1].format('YYYY-MM-DD');
      }

      const response = await inventoryService.getStockTransactions(params);
      setTransactions(response.results);
      setTotal(response.count);

    } catch (err) {
      console.error('❌ Error loading transactions:', err);
      setError('Failed to load stock transactions. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: string | number) => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(num);
  };

  const formatNumber = (value: string | number) => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-US', { maximumFractionDigits: 4 }).format(num);
  };

  const getTransactionTypeIcon = (type: string) => {
    switch (type) {
      case 'RECEIPT':
      case 'TRANSFER_IN':
      case 'RETURN':
        return <ArrowDownOutlined style={{ color: '#52c41a' }} />;
      case 'ISSUE':
      case 'TRANSFER_OUT':
      case 'DAMAGE':
        return <ArrowUpOutlined style={{ color: '#ff4d4f' }} />;
      case 'ADJUSTMENT':
      case 'CYCLE_COUNT':
        return <EditOutlined style={{ color: '#1890ff' }} />;
      default:
        return <SwapOutlined />;
    }
  };

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'RECEIPT': return 'green';
      case 'ISSUE': return 'red';
      case 'ADJUSTMENT': return 'blue';
      case 'TRANSFER_OUT': return 'orange';
      case 'TRANSFER_IN': return 'cyan';
      case 'RETURN': return 'purple';
      case 'DAMAGE': return 'magenta';
      case 'CYCLE_COUNT': return 'geekblue';
      default: return 'default';
    }
  };

  const getQuantityDisplay = (record: StockTransaction) => {
    const quantity = parseFloat(record.quantity);
    const isPositive = quantity > 0;
    
    return (
      <div className={`font-medium ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? '+' : ''}{formatNumber(quantity)}
      </div>
    );
  };

  const columns = [
    {
      title: 'Date',
      dataIndex: 'transaction_date',
      key: 'transaction_date',
      width: 120,
      render: (date: string) => (
        <div>
          <div>{new Date(date).toLocaleDateString()}</div>
          <div className="text-xs text-gray-500">
            {new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
        </div>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'transaction_type',
      key: 'transaction_type',
      width: 140,
      render: (type: string) => (
        <Tag 
          color={getTransactionTypeColor(type)} 
          icon={getTransactionTypeIcon(type)}
        >
          {type.replace('_', ' ')}
        </Tag>
      ),
    },
    {
      title: 'Product',
      key: 'product',
      width: 200,
      render: (_: any, record: StockTransaction) => (
        <div>
          <div className="font-medium">{record.product_code}</div>
          <div className="text-sm text-gray-500 truncate" style={{ maxWidth: 180 }}>
            {record.product_name}
          </div>
        </div>
      ),
    },
    {
      title: 'Warehouse',
      dataIndex: 'warehouse_code',
      key: 'warehouse_code',
      width: 120,
      render: (code: string, record: StockTransaction) => (
        <Tooltip title={record.warehouse_name}>
          <div className="font-medium">{code}</div>
        </Tooltip>
      ),
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      align: 'right' as const,
      render: (_: any, record: StockTransaction) => getQuantityDisplay(record),
    },
    {
      title: 'Unit Cost',
      dataIndex: 'unit_cost',
      key: 'unit_cost',
      width: 100,
      align: 'right' as const,
      render: (value: string) => formatCurrency(value),
    },
    {
      title: 'Total Cost',
      dataIndex: 'total_cost',
      key: 'total_cost',
      width: 120,
      align: 'right' as const,
      render: (value: string) => (
        <div className="font-medium">{formatCurrency(value)}</div>
      ),
    },
    {
      title: 'Balance',
      dataIndex: 'balance_quantity',
      key: 'balance_quantity',
      width: 100,
      align: 'right' as const,
      render: (value: string) => formatNumber(value),
    },
    {
      title: 'Reference',
      key: 'reference',
      width: 140,
      render: (_: any, record: StockTransaction) => (
        <div>
          {record.reference_number && (
            <div className="font-medium">{record.reference_number}</div>
          )}
          {record.reference_type && (
            <div className="text-xs text-gray-500">{record.reference_type}</div>
          )}
        </div>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      render: (text: string) => (
        <Tooltip title={text}>
          <div className="truncate" style={{ maxWidth: 180 }}>
            {text}
          </div>
        </Tooltip>
      ),
    },
    {
      title: 'Created By',
      dataIndex: 'created_by_name',
      key: 'created_by_name',
      width: 120,
    },
  ];

  const transactionTypes = [
    'RECEIPT',
    'ISSUE', 
    'ADJUSTMENT',
    'TRANSFER_OUT',
    'TRANSFER_IN',
    'RETURN',
    'DAMAGE',
    'CYCLE_COUNT'
  ];

  const referenceTypes = [
    'PO',
    'SO', 
    'INV',
    'ADJ',
    'TRF',
    'RET'
  ];

  // Calculate summary statistics
  const summaryStats = transactions.reduce(
    (acc, item) => {
      const quantity = parseFloat(item.quantity);
      const totalCost = parseFloat(item.total_cost);
      
      if (quantity > 0) {
        acc.inbound += quantity;
        acc.inboundValue += totalCost;
      } else {
        acc.outbound += Math.abs(quantity);
        acc.outboundValue += Math.abs(totalCost);
      }
      
      return acc;
    },
    { inbound: 0, outbound: 0, inboundValue: 0, outboundValue: 0 }
  );

  if (error) {
    return (
      <Alert
        message="Error"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={loadTransactions}>
            Retry
          </Button>
        }
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Stock Transactions</h1>
          <p className="text-gray-600">Complete audit trail of all inventory movements</p>
        </div>
        <Space>
          <Button icon={<ExportOutlined />}>Export</Button>
          <Button icon={<ReloadOutlined />} onClick={loadTransactions}>
            Refresh
          </Button>
        </Space>
      </div>

      {/* Summary Statistics */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Total Transactions"
              value={total}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Inbound Quantity"
              value={formatNumber(summaryStats.inbound)}
              valueStyle={{ color: '#52c41a' }}
              prefix={<ArrowDownOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Outbound Quantity"
              value={formatNumber(summaryStats.outbound)}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<ArrowUpOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Net Value"
              value={formatCurrency(summaryStats.inboundValue - summaryStats.outboundValue)}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={6}>
            <Input
              placeholder="Search transactions..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col xs={24} sm={4}>
            <Select
              placeholder="Warehouse"
              value={selectedWarehouse}
              onChange={setSelectedWarehouse}
              allowClear
              style={{ width: '100%' }}
            >
              {warehouses.map(warehouse => (
                <Option key={warehouse.warehouse_id} value={warehouse.warehouse_id}>
                  {warehouse.code}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={4}>
            <Select
              placeholder="Type"
              value={selectedTransactionType}
              onChange={setSelectedTransactionType}
              allowClear
              style={{ width: '100%' }}
            >
              {transactionTypes.map(type => (
                <Option key={type} value={type}>
                  {type.replace('_', ' ')}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={4}>
            <Select
              placeholder="Reference"
              value={selectedReferenceType}
              onChange={setSelectedReferenceType}
              allowClear
              style={{ width: '100%' }}
            >
              {referenceTypes.map(type => (
                <Option key={type} value={type}>
                  {type}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={4}>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
              style={{ width: '100%' }}
              placeholder={['Start Date', 'End Date']}
            />
          </Col>
          <Col xs={24} sm={2}>
            <Button 
              icon={<FilterOutlined />} 
              onClick={() => {
                setSearchText('');
                setSelectedWarehouse(undefined);
                setSelectedTransactionType(undefined);
                setSelectedReferenceType(undefined);
                setDateRange(null);
              }}
            >
              Clear
            </Button>
          </Col>
        </Row>
      </Card>

      {/* Transactions Table */}
      <Card>
        <Table
          dataSource={transactions}
          columns={columns}
          rowKey="transaction_id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} of ${total} transactions`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 50);
            },
          }}
          scroll={{ x: 1400 }}
          size="small"
        />
      </Card>
    </div>
  );
};

export default StockTransactionsPage;

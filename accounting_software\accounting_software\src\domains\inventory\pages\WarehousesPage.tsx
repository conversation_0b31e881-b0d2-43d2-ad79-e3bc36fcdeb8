/**
 * Warehouses Page
 * Multi-location warehouse management using Material-UI
 */

import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Chip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Menu,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Business as BusinessIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { inventoryService, Warehouse } from '../services/inventory.service';

const WarehousesPage: React.FC = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(true);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedType, setSelectedType] = useState<string>('');
  
  // Modal states
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingWarehouse, setEditingWarehouse] = useState<Warehouse | null>(null);
  const [modalLoading, setModalLoading] = useState(false);
  
  // Form states
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    warehouse_type: 'BRANCH',
    address: '',
    city: '',
    state: '',
    postal_code: '',
    country: 'USA',
    phone: '',
    email: '',
    manager: '',
    is_active: true,
    allow_negative_stock: false,
    is_default: false,
  });

  useEffect(() => {
    loadWarehouses();
  }, []);

  const loadWarehouses = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await inventoryService.getWarehouses();
      setWarehouses(data);
    } catch (err) {
      console.error('❌ Error loading warehouses:', err);
      setError('Failed to load warehouses. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      code: '',
      name: '',
      warehouse_type: 'BRANCH',
      address: '',
      city: '',
      state: '',
      postal_code: '',
      country: 'USA',
      phone: '',
      email: '',
      manager: '',
      is_active: true,
      allow_negative_stock: false,
      is_default: false,
    });
  };

  const handleCreate = () => {
    setEditingWarehouse(null);
    resetForm();
    setIsModalOpen(true);
  };

  const handleEdit = (warehouse: Warehouse) => {
    setEditingWarehouse(warehouse);
    setFormData({
      code: warehouse.code,
      name: warehouse.name,
      warehouse_type: warehouse.warehouse_type,
      address: warehouse.address || '',
      city: warehouse.city || '',
      state: warehouse.state || '',
      postal_code: warehouse.postal_code || '',
      country: warehouse.country,
      phone: warehouse.phone || '',
      email: warehouse.email || '',
      manager: warehouse.manager?.toString() || '',
      is_active: warehouse.is_active,
      allow_negative_stock: warehouse.allow_negative_stock,
      is_default: warehouse.is_default,
    });
    setIsModalOpen(true);
  };

  const handleDelete = (warehouse: Warehouse) => {
    if (window.confirm(`Are you sure you want to delete warehouse "${warehouse.code} - ${warehouse.name}"?`)) {
      deleteWarehouse(warehouse);
    }
  };

  const deleteWarehouse = async (warehouse: Warehouse) => {
    try {
      await inventoryService.deleteWarehouse(warehouse.warehouse_id);
      enqueueSnackbar('Warehouse deleted successfully', { variant: 'success' });
      loadWarehouses();
    } catch (err) {
      console.error('❌ Error deleting warehouse:', err);
      enqueueSnackbar('Failed to delete warehouse', { variant: 'error' });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setModalLoading(true);
      
      const submitData = {
        ...formData,
        manager: formData.manager ? parseInt(formData.manager) : undefined,
      };
      
      if (editingWarehouse) {
        await inventoryService.updateWarehouse(editingWarehouse.warehouse_id, submitData);
        enqueueSnackbar('Warehouse updated successfully', { variant: 'success' });
      } else {
        await inventoryService.createWarehouse(submitData);
        enqueueSnackbar('Warehouse created successfully', { variant: 'success' });
      }
      
      setIsModalOpen(false);
      loadWarehouses();
    } catch (err) {
      console.error('❌ Error saving warehouse:', err);
      enqueueSnackbar('Failed to save warehouse', { variant: 'error' });
    } finally {
      setModalLoading(false);
    }
  };

  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  const handleSelectChange = (field: string) => (event: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  const handleSwitchChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.checked
    }));
  };

  const getWarehouseTypeColor = (type: string): "primary" | "secondary" | "success" | "error" | "info" | "warning" => {
    switch (type) {
      case 'MAIN': return 'primary';
      case 'BRANCH': return 'success';
      case 'VIRTUAL': return 'secondary';
      case 'DAMAGED': return 'error';
      default: return 'info';
    }
  };

  const getWarehouseTypeIcon = (type: string) => {
    switch (type) {
      case 'MAIN': return <BusinessIcon />;
      case 'BRANCH': return <LocationIcon />;
      case 'VIRTUAL': return <BusinessIcon />;
      case 'DAMAGED': return <BusinessIcon />;
      default: return <BusinessIcon />;
    }
  };

  const filteredWarehouses = warehouses.filter(warehouse => {
    const matchesSearch = !searchText || 
      warehouse.code.toLowerCase().includes(searchText.toLowerCase()) ||
      warehouse.name.toLowerCase().includes(searchText.toLowerCase()) ||
      warehouse.city?.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesType = !selectedType || warehouse.warehouse_type === selectedType;
    
    return matchesSearch && matchesType;
  });

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert 
          severity="error" 
          action={
            <Button size="small" onClick={loadWarehouses}>
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Warehouses
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage warehouse locations and settings
          </Typography>
        </Box>
        <Button 
          variant="contained" 
          startIcon={<AddIcon />}
          onClick={handleCreate}
        >
          New Warehouse
        </Button>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                placeholder="Search warehouses..."
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <InputLabel>Filter by type</InputLabel>
                <Select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  label="Filter by type"
                >
                  <MenuItem value="">All Types</MenuItem>
                  <MenuItem value="MAIN">Main Warehouse</MenuItem>
                  <MenuItem value="BRANCH">Branch Location</MenuItem>
                  <MenuItem value="VIRTUAL">Virtual/Consignment</MenuItem>
                  <MenuItem value="DAMAGED">Damaged Goods</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={2}>
              <Button 
                fullWidth
                variant="outlined"
                onClick={() => {
                  setSearchText('');
                  setSelectedType('');
                }}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Warehouses Table */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Warehouses ({filteredWarehouses.length})
            </Typography>
            <IconButton onClick={loadWarehouses} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Box>
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Code</TableCell>
                    <TableCell>Name</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Location</TableCell>
                    <TableCell>Contact</TableCell>
                    <TableCell>Settings</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredWarehouses.map((warehouse) => (
                    <TableRow key={warehouse.warehouse_id}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {warehouse.code}
                        </Typography>
                      </TableCell>
                      <TableCell>{warehouse.name}</TableCell>
                      <TableCell>
                        <Chip
                          icon={getWarehouseTypeIcon(warehouse.warehouse_type)}
                          label={warehouse.warehouse_type}
                          color={getWarehouseTypeColor(warehouse.warehouse_type)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Box>
                          {warehouse.address && (
                            <Typography variant="body2">{warehouse.address}</Typography>
                          )}
                          <Typography variant="body2" color="text.secondary">
                            {[warehouse.city, warehouse.state, warehouse.postal_code].filter(Boolean).join(', ')}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          {warehouse.phone && (
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                              <PhoneIcon sx={{ fontSize: 16, mr: 0.5 }} />
                              <Typography variant="body2">{warehouse.phone}</Typography>
                            </Box>
                          )}
                          {warehouse.email && (
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <EmailIcon sx={{ fontSize: 16, mr: 0.5 }} />
                              <Typography variant="body2">{warehouse.email}</Typography>
                            </Box>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          {warehouse.is_default && (
                            <Chip label="Default" color="warning" size="small" sx={{ mb: 0.5 }} />
                          )}
                          {warehouse.allow_negative_stock && (
                            <Chip label="Negative Stock" color="info" size="small" />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={warehouse.is_active ? 'Active' : 'Inactive'}
                          color={warehouse.is_active ? 'success' : 'error'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(warehouse.created_at).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Box>
                          <Tooltip title="Edit">
                            <IconButton 
                              size="small" 
                              onClick={() => handleEdit(warehouse)}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton 
                              size="small" 
                              color="error"
                              onClick={() => handleDelete(warehouse)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Create/Edit Modal */}
      <Dialog
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <form onSubmit={handleSubmit}>
          <DialogTitle>
            {editingWarehouse ? 'Edit Warehouse' : 'Create Warehouse'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Warehouse Code"
                  value={formData.code}
                  onChange={handleInputChange('code')}
                  required
                  placeholder="e.g., MAIN, BR001"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Warehouse Name"
                  value={formData.name}
                  onChange={handleInputChange('name')}
                  required
                  placeholder="e.g., Main Warehouse"
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth required>
                  <InputLabel>Warehouse Type</InputLabel>
                  <Select
                    value={formData.warehouse_type}
                    onChange={handleSelectChange('warehouse_type')}
                    label="Warehouse Type"
                  >
                    <MenuItem value="MAIN">Main Warehouse</MenuItem>
                    <MenuItem value="BRANCH">Branch Location</MenuItem>
                    <MenuItem value="VIRTUAL">Virtual/Consignment</MenuItem>
                    <MenuItem value="DAMAGED">Damaged Goods</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Manager"
                  value={formData.manager}
                  onChange={handleInputChange('manager')}
                  placeholder="Manager name or ID"
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Address"
                  value={formData.address}
                  onChange={handleInputChange('address')}
                  multiline
                  rows={2}
                  placeholder="Street address"
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="City"
                  value={formData.city}
                  onChange={handleInputChange('city')}
                  placeholder="City"
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="State"
                  value={formData.state}
                  onChange={handleInputChange('state')}
                  placeholder="State"
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Postal Code"
                  value={formData.postal_code}
                  onChange={handleInputChange('postal_code')}
                  placeholder="Postal code"
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Country"
                  value={formData.country}
                  onChange={handleInputChange('country')}
                  placeholder="Country"
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Phone"
                  value={formData.phone}
                  onChange={handleInputChange('phone')}
                  placeholder="Phone number"
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange('email')}
                  placeholder="Email address"
                />
              </Grid>

              <Grid item xs={12} sm={4}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.is_active}
                      onChange={handleSwitchChange('is_active')}
                    />
                  }
                  label="Active"
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.allow_negative_stock}
                      onChange={handleSwitchChange('allow_negative_stock')}
                    />
                  }
                  label="Allow Negative Stock"
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.is_default}
                      onChange={handleSwitchChange('is_default')}
                    />
                  }
                  label="Default Warehouse"
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setIsModalOpen(false)}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={modalLoading}
            >
              {modalLoading ? <CircularProgress size={20} /> : (editingWarehouse ? 'Update' : 'Create')}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </Box>
  );
};

export default WarehousesPage;

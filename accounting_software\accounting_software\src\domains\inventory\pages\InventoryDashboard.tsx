/**
 * Enterprise Inventory Dashboard
 * Following our established patterns with comprehensive overview using Material-UI
 */

import React, { useState, useEffect } from 'react';
import {
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Button,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
} from '@mui/material';
import {
  Business as BusinessIcon,
  Inventory as InventoryIcon,
  Warning as WarningIcon,
  Stop as StopIcon,
  SwapHoriz as SwapIcon,
  Edit as EditIcon,
  AttachMoney as MoneyIcon,
  BarChart as ChartIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { inventoryService, InventorySummary, WarehouseSummary, StockLevel } from '../services/inventory.service';

const InventoryDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [inventorySummary, setInventorySummary] = useState<InventorySummary | null>(null);
  const [warehouseSummary, setWarehouseSummary] = useState<WarehouseSummary[]>([]);
  const [lowStockItems, setLowStockItems] = useState<StockLevel[]>([]);
  const [outOfStockItems, setOutOfStockItems] = useState<StockLevel[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [summary, warehouses, lowStock, outOfStock] = await Promise.all([
        inventoryService.getInventorySummary(),
        inventoryService.getWarehouseSummary(),
        inventoryService.getLowStockItems(),
        inventoryService.getOutOfStockItems()
      ]);

      setInventorySummary(summary);
      setWarehouseSummary(warehouses);
      setLowStockItems(lowStock.slice(0, 10)); // Show top 10
      setOutOfStockItems(outOfStock.slice(0, 10)); // Show top 10

    } catch (err) {
      console.error('❌ Error loading dashboard data:', err);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: string | number) => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(num);
  };

  const formatNumber = (value: string | number) => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-US').format(num);
  };

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    color: string;
  }> = ({ title, value, icon, color }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography color="text.secondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" sx={{ color }}>
              {value}
            </Typography>
          </Box>
          <Box sx={{ color, fontSize: 40 }}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert 
          severity="error" 
          action={
            <Button size="small" onClick={loadDashboardData}>
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Inventory Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Enterprise inventory management overview
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button 
            variant="contained" 
            startIcon={<EditIcon />}
            onClick={() => navigate('/dashboard/inventory/adjustments/new')}
          >
            New Adjustment
          </Button>
          <Button 
            variant="outlined"
            startIcon={<SwapIcon />}
            onClick={() => navigate('/dashboard/inventory/transfers/new')}
          >
            New Transfer
          </Button>
          <IconButton onClick={loadDashboardData}>
            <RefreshIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Summary Statistics */}
      {inventorySummary && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Warehouses"
              value={inventorySummary.total_warehouses}
              icon={<BusinessIcon />}
              color="#1976d2"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Products"
              value={inventorySummary.total_products}
              icon={<InventoryIcon />}
              color="#2e7d32"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Quantity"
              value={formatNumber(inventorySummary.total_quantity)}
              icon={<ChartIcon />}
              color="#7b1fa2"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Value"
              value={formatCurrency(inventorySummary.total_value)}
              icon={<MoneyIcon />}
              color="#f57c00"
            />
          </Grid>
        </Grid>
      )}

      {/* Alert Statistics */}
      {inventorySummary && (inventorySummary.low_stock_items > 0 || inventorySummary.out_of_stock_items > 0) && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Low Stock Items
                    </Typography>
                    <Typography variant="h4" component="div" sx={{ color: '#f57c00' }}>
                      {inventorySummary.low_stock_items}
                    </Typography>
                    <Button 
                      size="small"
                      onClick={() => navigate('/dashboard/inventory/stock-levels?filter=low_stock')}
                    >
                      View Details
                    </Button>
                  </Box>
                  <Box sx={{ color: '#f57c00', fontSize: 40 }}>
                    <WarningIcon />
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Out of Stock Items
                    </Typography>
                    <Typography variant="h4" component="div" sx={{ color: '#d32f2f' }}>
                      {inventorySummary.out_of_stock_items}
                    </Typography>
                    <Button 
                      size="small"
                      onClick={() => navigate('/dashboard/inventory/stock-levels?filter=out_of_stock')}
                    >
                      View Details
                    </Button>
                  </Box>
                  <Box sx={{ color: '#d32f2f', fontSize: 40 }}>
                    <StopIcon />
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Warehouse Summary */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Warehouse Overview
            </Typography>
            <Button 
              size="small"
              onClick={() => navigate('/dashboard/inventory/warehouses')}
            >
              View All
            </Button>
          </Box>
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Warehouse</TableCell>
                  <TableCell align="right">Products</TableCell>
                  <TableCell align="right">Total Quantity</TableCell>
                  <TableCell align="right">Total Value</TableCell>
                  <TableCell>Alerts</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {warehouseSummary.map((warehouse) => (
                  <TableRow key={warehouse.warehouse_id}>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {warehouse.warehouse_code}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {warehouse.warehouse_name}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="right">{formatNumber(warehouse.total_products)}</TableCell>
                    <TableCell align="right">{formatNumber(warehouse.total_quantity)}</TableCell>
                    <TableCell align="right">{formatCurrency(warehouse.total_value)}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        {warehouse.low_stock_items > 0 && (
                          <Chip 
                            icon={<WarningIcon />}
                            label={`${warehouse.low_stock_items} Low`}
                            color="warning"
                            size="small"
                          />
                        )}
                        {warehouse.out_of_stock_items > 0 && (
                          <Chip 
                            icon={<StopIcon />}
                            label={`${warehouse.out_of_stock_items} Out`}
                            color="error"
                            size="small"
                          />
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Quick Actions
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Button 
                fullWidth 
                variant="outlined"
                size="large"
                onClick={() => navigate('/dashboard/inventory/stock-levels')}
              >
                View Stock Levels
              </Button>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Button 
                fullWidth 
                variant="outlined"
                size="large"
                onClick={() => navigate('/dashboard/inventory/transactions')}
              >
                View Transactions
              </Button>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Button 
                fullWidth 
                variant="outlined"
                size="large"
                onClick={() => navigate('/dashboard/inventory/transfers')}
              >
                View Transfers
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default InventoryDashboard;

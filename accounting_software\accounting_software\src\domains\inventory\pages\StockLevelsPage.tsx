/**
 * Stock Levels Page
 * Enterprise inventory stock level management
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Input, 
  Select, 
  Tag, 
  Row, 
  Col, 
  Statistic,
  Alert,
  Spin,
  Tooltip,
  Progress
} from 'antd';
import { 
  SearchOutlined, 
  ReloadOutlined, 
  ExclamationTriangleOutlined,
  StopOutlined,
  CheckCircleOutlined,
  FilterOutlined,
  ExportOutlined
} from '@ant-design/icons';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { inventoryService, StockLevel, Warehouse } from '../services/inventory.service';

const { Option } = Select;

const StockLevelsPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  
  const [loading, setLoading] = useState(true);
  const [stockLevels, setStockLevels] = useState<StockLevel[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(25);
  const [error, setError] = useState<string | null>(null);

  // Filters
  const [searchText, setSearchText] = useState('');
  const [selectedWarehouse, setSelectedWarehouse] = useState<number | undefined>();
  const [stockFilter, setStockFilter] = useState<string>('all');

  useEffect(() => {
    loadWarehouses();
  }, []);

  useEffect(() => {
    // Load filters from URL params
    const filter = searchParams.get('filter');
    const warehouse = searchParams.get('warehouse');
    const search = searchParams.get('search');

    if (filter) setStockFilter(filter);
    if (warehouse) setSelectedWarehouse(parseInt(warehouse));
    if (search) setSearchText(search);

    loadStockLevels();
  }, [currentPage, pageSize, searchText, selectedWarehouse, stockFilter, searchParams]);

  const loadWarehouses = async () => {
    try {
      const data = await inventoryService.getWarehouses();
      setWarehouses(data);
    } catch (err) {
      console.error('❌ Error loading warehouses:', err);
    }
  };

  const loadStockLevels = async () => {
    try {
      setLoading(true);
      setError(null);

      const params: any = {
        page: currentPage,
        page_size: pageSize,
      };

      if (searchText) params.search = searchText;
      if (selectedWarehouse) params.warehouse = selectedWarehouse;
      if (stockFilter === 'low_stock') params.low_stock = true;
      if (stockFilter === 'out_of_stock') params.out_of_stock = true;

      const response = await inventoryService.getStockLevels(params);
      setStockLevels(response.results);
      setTotal(response.count);

    } catch (err) {
      console.error('❌ Error loading stock levels:', err);
      setError('Failed to load stock levels. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
    setCurrentPage(1);
    updateUrlParams({ search: value || undefined });
  };

  const handleWarehouseChange = (value: number | undefined) => {
    setSelectedWarehouse(value);
    setCurrentPage(1);
    updateUrlParams({ warehouse: value?.toString() });
  };

  const handleStockFilterChange = (value: string) => {
    setStockFilter(value);
    setCurrentPage(1);
    updateUrlParams({ filter: value === 'all' ? undefined : value });
  };

  const updateUrlParams = (updates: Record<string, string | undefined>) => {
    const newParams = new URLSearchParams(searchParams);
    
    Object.entries(updates).forEach(([key, value]) => {
      if (value) {
        newParams.set(key, value);
      } else {
        newParams.delete(key);
      }
    });

    setSearchParams(newParams);
  };

  const formatCurrency = (value: string | number) => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(num);
  };

  const formatNumber = (value: string | number) => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-US', { maximumFractionDigits: 4 }).format(num);
  };

  const getStockStatus = (record: StockLevel) => {
    if (record.is_out_of_stock) {
      return <Tag color="red" icon={<StopOutlined />}>Out of Stock</Tag>;
    } else if (record.is_below_reorder_point) {
      return <Tag color="orange" icon={<ExclamationTriangleOutlined />}>Low Stock</Tag>;
    } else {
      return <Tag color="green" icon={<CheckCircleOutlined />}>In Stock</Tag>;
    }
  };

  const getStockProgress = (record: StockLevel) => {
    const onHand = parseFloat(record.quantity_on_hand);
    const reorderPoint = parseFloat(record.reorder_point);
    const maxLevel = parseFloat(record.max_stock_level);

    if (maxLevel > 0) {
      const percentage = (onHand / maxLevel) * 100;
      let status: 'success' | 'normal' | 'exception' = 'success';
      
      if (onHand <= 0) status = 'exception';
      else if (onHand <= reorderPoint) status = 'normal';

      return (
        <Progress 
          percent={Math.min(percentage, 100)} 
          status={status}
          size="small"
          showInfo={false}
        />
      );
    }
    return null;
  };

  const columns = [
    {
      title: 'Product',
      key: 'product',
      width: 200,
      render: (_: any, record: StockLevel) => (
        <div>
          <div className="font-medium">{record.product_code}</div>
          <div className="text-sm text-gray-500 truncate" style={{ maxWidth: 180 }}>
            {record.product_name}
          </div>
        </div>
      ),
    },
    {
      title: 'Warehouse',
      dataIndex: 'warehouse_code',
      key: 'warehouse_code',
      width: 120,
      render: (code: string, record: StockLevel) => (
        <Tooltip title={record.warehouse_name}>
          <div className="font-medium">{code}</div>
        </Tooltip>
      ),
    },
    {
      title: 'On Hand',
      dataIndex: 'quantity_on_hand',
      key: 'quantity_on_hand',
      width: 100,
      align: 'right' as const,
      render: (value: string) => formatNumber(value),
    },
    {
      title: 'Reserved',
      dataIndex: 'quantity_reserved',
      key: 'quantity_reserved',
      width: 100,
      align: 'right' as const,
      render: (value: string) => formatNumber(value),
    },
    {
      title: 'Available',
      dataIndex: 'available_quantity',
      key: 'available_quantity',
      width: 100,
      align: 'right' as const,
      render: (value: string, record: StockLevel) => (
        <div className="font-medium">
          {formatNumber(value)}
        </div>
      ),
    },
    {
      title: 'Reorder Point',
      dataIndex: 'reorder_point',
      key: 'reorder_point',
      width: 120,
      align: 'right' as const,
      render: (value: string) => formatNumber(value),
    },
    {
      title: 'Stock Level',
      key: 'stock_level',
      width: 120,
      render: (_: any, record: StockLevel) => getStockProgress(record),
    },
    {
      title: 'Status',
      key: 'status',
      width: 120,
      render: (_: any, record: StockLevel) => getStockStatus(record),
    },
    {
      title: 'Value',
      key: 'value',
      width: 120,
      align: 'right' as const,
      render: (_: any, record: StockLevel) => (
        <Tooltip title={`Avg: ${formatCurrency(record.stock_value_average)} | Last: ${formatCurrency(record.stock_value_last)}`}>
          <div>{formatCurrency(record.stock_value_average)}</div>
        </Tooltip>
      ),
    },
    {
      title: 'Last Updated',
      dataIndex: 'last_updated',
      key: 'last_updated',
      width: 120,
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
  ];

  // Calculate summary statistics
  const summaryStats = stockLevels.reduce(
    (acc, item) => {
      acc.totalValue += parseFloat(item.stock_value_average);
      acc.totalQuantity += parseFloat(item.quantity_on_hand);
      if (item.is_out_of_stock) acc.outOfStock++;
      else if (item.is_below_reorder_point) acc.lowStock++;
      else acc.inStock++;
      return acc;
    },
    { totalValue: 0, totalQuantity: 0, inStock: 0, lowStock: 0, outOfStock: 0 }
  );

  if (error) {
    return (
      <Alert
        message="Error"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={loadStockLevels}>
            Retry
          </Button>
        }
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Stock Levels</h1>
          <p className="text-gray-600">Monitor inventory levels across all warehouses</p>
        </div>
        <Space>
          <Button icon={<ExportOutlined />}>Export</Button>
          <Button icon={<ReloadOutlined />} onClick={loadStockLevels}>
            Refresh
          </Button>
        </Space>
      </div>

      {/* Summary Statistics */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Total Items"
              value={total}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Total Quantity"
              value={formatNumber(summaryStats.totalQuantity)}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Total Value"
              value={formatCurrency(summaryStats.totalValue)}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-green-600">In Stock:</span>
                <span className="font-medium">{summaryStats.inStock}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-orange-600">Low Stock:</span>
                <span className="font-medium">{summaryStats.lowStock}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-red-600">Out of Stock:</span>
                <span className="font-medium">{summaryStats.outOfStock}</span>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8}>
            <Input
              placeholder="Search products..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => handleSearch(e.target.value)}
              allowClear
            />
          </Col>
          <Col xs={24} sm={6}>
            <Select
              placeholder="Select warehouse"
              value={selectedWarehouse}
              onChange={handleWarehouseChange}
              allowClear
              style={{ width: '100%' }}
            >
              {warehouses.map(warehouse => (
                <Option key={warehouse.warehouse_id} value={warehouse.warehouse_id}>
                  {warehouse.code} - {warehouse.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={6}>
            <Select
              value={stockFilter}
              onChange={handleStockFilterChange}
              style={{ width: '100%' }}
            >
              <Option value="all">All Items</Option>
              <Option value="low_stock">Low Stock</Option>
              <Option value="out_of_stock">Out of Stock</Option>
            </Select>
          </Col>
          <Col xs={24} sm={4}>
            <Button 
              icon={<FilterOutlined />} 
              onClick={() => {
                setSearchText('');
                setSelectedWarehouse(undefined);
                setStockFilter('all');
                setSearchParams({});
              }}
            >
              Clear
            </Button>
          </Col>
        </Row>
      </Card>

      {/* Stock Levels Table */}
      <Card>
        <Table
          dataSource={stockLevels}
          columns={columns}
          rowKey="stock_level_id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} of ${total} items`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 25);
            },
          }}
          scroll={{ x: 1200 }}
          size="small"
        />
      </Card>
    </div>
  );
};

export default StockLevelsPage;

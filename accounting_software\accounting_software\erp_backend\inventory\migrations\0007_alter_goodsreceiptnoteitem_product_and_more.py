# Generated by Django 4.2.21 on 2025-06-29 12:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('Pricing', '0004_product_auto_pricing_enabled_and_more'),
        ('inventory', '0006_alter_inventory_product_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='goodsreceiptnoteitem',
            name='product',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='grn_items', to='Pricing.product'),
        ),
        migrations.AlterField(
            model_name='goodsreturnnoteitem',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grn_return_items', to='Pricing.product'),
        ),
        migrations.AlterField(
            model_name='inventorytransferitem',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfer_items', to='Pricing.product'),
        ),
    ]

# Generated by Django 4.2.21 on 2025-06-29 08:07

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('sales', '0012_remove_invoicelineitem_invoice_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='deliverynoteitem',
            name='delivery_note',
        ),
        migrations.RemoveField(
            model_name='deliverynoteitem',
            name='product',
        ),
        migrations.RemoveField(
            model_name='deliverynoteitem',
            name='sales_order_line_item',
        ),
        migrations.RemoveField(
            model_name='estimate',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='estimate',
            name='customer',
        ),
        migrations.RemoveField(
            model_name='estimatelineitem',
            name='estimate',
        ),
        migrations.RemoveField(
            model_name='estimatelineitem',
            name='product',
        ),
        migrations.RemoveField(
            model_name='salesorder',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='salesorder',
            name='customer',
        ),
        migrations.RemoveField(
            model_name='salesorderlineitem',
            name='product',
        ),
        migrations.RemoveField(
            model_name='salesorderlineitem',
            name='sales_order',
        ),
        migrations.DeleteModel(
            name='DeliveryNote',
        ),
        migrations.DeleteModel(
            name='DeliveryNoteItem',
        ),
        migrations.DeleteModel(
            name='Estimate',
        ),
        migrations.DeleteModel(
            name='EstimateLineItem',
        ),
        migrations.DeleteModel(
            name='SalesOrder',
        ),
        migrations.DeleteModel(
            name='SalesOrderLineItem',
        ),
    ]

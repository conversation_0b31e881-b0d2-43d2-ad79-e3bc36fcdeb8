# Generated by Django 4.2.21 on 2025-06-29 12:20

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Pricing', '0003_alter_pricelistitem_product'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='auto_pricing_enabled',
            field=models.BooleanField(default=False, help_text='Enable automatic price calculation based on cost'),
        ),
        migrations.AddField(
            model_name='product',
            name='markup_percentage',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Markup % over cost (e.g., 25.00 for 25%)', max_digits=5, validators=[django.core.validators.MinValueValidator(0)]),
        ),
        migrations.AddField(
            model_name='product',
            name='minimum_margin',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Minimum margin % required', max_digits=5, validators=[django.core.validators.MinValueValidator(0)]),
        ),
    ]

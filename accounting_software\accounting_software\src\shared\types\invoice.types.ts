// Invoice Types - Based on Backend Models
// Matches the Django Invoice and InvoiceLineItem models exactly

export interface Invoice {
  // Core Invoice Header Fields
  invoice_id: string;
  invoice_number: string;
  customer: number; // Customer ID
  customer_name?: string; // For display purposes
  invoice_date: string;
  due_date: string;
  status: InvoiceStatus;
  
  // Payment Terms
  payment_terms: number; // PaymentTerm ID
  payment_terms_name?: string; // For display purposes
  
  // Reference Information
  po_number?: string;
  sales_rep?: number; // User ID
  sales_rep_name?: string; // For display purposes
  
  // Financial Fields
  subtotal_amount: string;
  tax_amount: string;
  discount_amount: string;
  shipping_amount: string;
  total_amount: string;
  amount_paid: string;
  balance_due: string;
  
  // Additional Information
  memo?: string;
  terms_conditions?: string;
  footer_text?: string;
  
  // Timestamps
  created_at: string;
  updated_at: string;
  created_by?: number; // User ID
  created_by_name?: string; // For display purposes
  
  // Computed Properties
  is_overdue?: boolean;
  days_overdue?: number;
  
  // Line Items
  line_items: InvoiceLineItem[];
}

export interface InvoiceLineItem {
  id?: number;
  invoice: number; // Invoice ID
  product: number; // Product ID
  product_code?: string; // For display purposes
  product_name?: string; // For display purposes
  description?: string;
  quantity: string;
  unit_price: string;
  line_total: string;
  tax_rate?: string;
  tax_amount?: string;
  discount_percentage?: string;
  discount_amount?: string;
  
  // Computed Properties
  total_with_tax?: string;
  
  // Inventory Information (for display)
  available_quantity?: string;
  warehouse_info?: WarehouseInfo;
}

export interface WarehouseInfo {
  warehouse_id: number;
  warehouse_name: string;
  warehouse_code: string;
  available_quantity: string;
  is_sufficient: boolean;
}

export interface InventoryCheckResult {
  is_available: boolean;
  available_qty: string;
  shortage: string;
  warehouse_info: WarehouseInfo;
}

export interface InvoiceInventoryReport {
  line_item_id: number;
  product_code: string;
  product_name: string;
  required_quantity: string;
  available_quantity: string;
  shortage: string;
  is_available: boolean;
  warehouse_info: WarehouseInfo;
}

export interface InvoiceInventorySummary {
  invoice_number: string;
  total_line_items: number;
  items_with_sufficient_stock: number;
  items_with_insufficient_stock: number;
  all_items_available: boolean;
  line_items: any[];
}

// Invoice Status Enum
export type InvoiceStatus = 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled' | 'partial';

export const INVOICE_STATUS_OPTIONS = [
  { value: 'draft', label: 'Draft', color: 'default' },
  { value: 'sent', label: 'Sent', color: 'info' },
  { value: 'paid', label: 'Paid', color: 'success' },
  { value: 'overdue', label: 'Overdue', color: 'error' },
  { value: 'cancelled', label: 'Cancelled', color: 'default' },
  { value: 'partial', label: 'Partially Paid', color: 'warning' },
] as const;

// Form Data Types
export interface InvoiceFormData {
  customer: number | null;
  invoice_date: string;
  payment_terms: number | null;
  po_number?: string;
  sales_rep?: number | null;
  discount_amount?: string;
  shipping_amount?: string;
  memo?: string;
  terms_conditions?: string;
  footer_text?: string;
  line_items: InvoiceLineItemFormData[];
}

export interface InvoiceLineItemFormData {
  product: number | null;
  description?: string;
  quantity: string;
  unit_price?: string;
  tax_rate?: string;
  discount_percentage?: string;
}

// API Response Types
export interface InvoiceListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Invoice[];
}

export interface InvoiceCreateResponse {
  success: boolean;
  invoice: Invoice;
  message: string;
}

export interface InvoiceUpdateResponse {
  success: boolean;
  invoice: Invoice;
  message: string;
}

export interface InvoiceDeleteResponse {
  success: boolean;
  message: string;
}

// Filter and Search Types
export interface InvoiceFilters {
  status?: InvoiceStatus;
  customer?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
  page?: number;
  page_size?: number;
}

// Customer Option for Autocomplete
export interface CustomerOption {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  balance?: string;
}

// Product Option for Autocomplete
export interface ProductOption {
  id: number;
  code: string;
  name: string;
  description?: string;
  sale_price: string;
  cost_price: string;
  tax_code?: number;
  tax_rate?: string;
  available_quantity?: string;
}

// Payment Terms Option
export interface PaymentTermOption {
  id: number;
  name: string;
  days: number;
  is_active: boolean;
}

// Sales Rep Option
export interface SalesRepOption {
  id: number;
  username: string;
  first_name: string;
  last_name: string;
  full_name: string;
}

// Invoice Statistics
export interface InvoiceStats {
  total_invoices: number;
  draft_invoices: number;
  sent_invoices: number;
  paid_invoices: number;
  overdue_invoices: number;
  total_amount: string;
  paid_amount: string;
  outstanding_amount: string;
}

// Export types for easier imports
export type {
  Invoice as InvoiceType,
  InvoiceLineItem as InvoiceLineItemType,
  InvoiceFormData as InvoiceFormDataType,
  InvoiceLineItemFormData as InvoiceLineItemFormDataType,
};

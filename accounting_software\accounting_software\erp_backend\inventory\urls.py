"""
Enterprise Inventory URLs
Following our established patterns
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    WarehouseViewSet, StockLevelViewSet, StockTransactionViewSet,
    StockAdjustmentViewSet, StockTransferViewSet, StockValuationViewSet
)

router = DefaultRouter()
router.register(r'warehouses', WarehouseViewSet, basename='warehouse')
router.register(r'stock-levels', StockLevelViewSet, basename='stocklevel')
router.register(r'stock-transactions', StockTransactionViewSet, basename='stocktransaction')
router.register(r'stock-adjustments', StockAdjustmentViewSet, basename='stockadjustment')
router.register(r'stock-transfers', StockTransferViewSet, basename='stocktransfer')
router.register(r'stock-valuations', StockValuationViewSet, basename='stockvaluation')

urlpatterns = [
    path('', include(router.urls)),
]

#!/usr/bin/env python
"""
Create sample invoice data for testing
"""
import os
import django
from datetime import date, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from sales.models import Invoice, PaymentTerm
from contacts.models import Contact
from django.contrib.auth.models import User

def create_sample_invoices():
    print('🧪 Creating Sample Invoice Data...')
    
    # Get required data
    customers = Contact.objects.filter(contact_type='customer')[:5]
    payment_terms = PaymentTerm.objects.filter(is_active=True)
    users = User.objects.all()
    
    if not customers.exists():
        print('❌ No customer contacts found. Please create some customers first.')
        return
    
    if not payment_terms.exists():
        print('❌ No payment terms found. Please create payment terms first.')
        return
    
    print(f'✅ Found {customers.count()} customers')
    print(f'✅ Found {payment_terms.count()} payment terms')
    
    # Sample invoice data
    sample_invoices = [
        {
            'customer': customers[0],
            'invoice_date': date.today() - timedelta(days=30),
            'status': 'paid',
            'payment_terms': payment_terms.filter(days=30).first() or payment_terms.first(),
            'po_number': 'PO-2024-001',
            'sales_rep': users.first() if users.exists() else None,
        },
        {
            'customer': customers[1] if customers.count() > 1 else customers[0],
            'invoice_date': date.today() - timedelta(days=15),
            'status': 'sent',
            'payment_terms': payment_terms.filter(days=15).first() or payment_terms.first(),
            'po_number': 'PO-2024-002',
            'sales_rep': users.first() if users.exists() else None,
        },
        {
            'customer': customers[2] if customers.count() > 2 else customers[0],
            'invoice_date': date.today() - timedelta(days=45),
            'status': 'overdue',
            'payment_terms': payment_terms.filter(days=30).first() or payment_terms.first(),
            'po_number': 'PO-2024-003',
            'sales_rep': users.first() if users.exists() else None,
        },
        {
            'customer': customers[3] if customers.count() > 3 else customers[0],
            'invoice_date': date.today() - timedelta(days=5),
            'status': 'sent',
            'payment_terms': payment_terms.filter(days=0).first() or payment_terms.first(),
            'po_number': 'PO-2024-004',
            'sales_rep': users.first() if users.exists() else None,
        },
        {
            'customer': customers[4] if customers.count() > 4 else customers[0],
            'invoice_date': date.today(),
            'status': 'draft',
            'payment_terms': payment_terms.filter(days=30).first() or payment_terms.first(),
            'po_number': 'PO-2024-005',
            'sales_rep': users.first() if users.exists() else None,
        },
    ]
    
    created_count = 0
    for invoice_data in sample_invoices:
        try:
            # Create invoice
            invoice = Invoice.objects.create(
                customer=invoice_data['customer'],
                invoice_date=invoice_data['invoice_date'],
                status=invoice_data['status'],
                payment_terms=invoice_data['payment_terms'],
                po_number=invoice_data['po_number'],
                sales_rep=invoice_data['sales_rep'],
                created_by=invoice_data['sales_rep'],
            )
            
            print(f'✅ Created invoice: {invoice.invoice_number} for {invoice.customer.name}')
            created_count += 1
            
        except Exception as e:
            print(f'❌ Error creating invoice: {e}')
    
    print(f'\n🎯 Successfully created {created_count} sample invoices!')
    print('📋 You can now view them in Django admin at: http://localhost:8000/admin/sales/invoice/')
    
    # Show summary
    total_invoices = Invoice.objects.count()
    print(f'📊 Total invoices in database: {total_invoices}')
    
    # Show status breakdown
    for status, label in Invoice.STATUS_CHOICES:
        count = Invoice.objects.filter(status=status).count()
        if count > 0:
            print(f'   {label}: {count} invoices')

if __name__ == '__main__':
    create_sample_invoices()

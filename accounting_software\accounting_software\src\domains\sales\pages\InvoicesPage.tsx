import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Typography,
  Box,
  Grid,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Chip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Menu,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Email as EmailIcon,
  Payment as PaymentIcon,
  MoreVert as MoreVertIcon,
  AttachMoney as MoneyIcon,
  Receipt as ReceiptIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { invoiceService, Invoice, InvoiceFilters, InvoiceStats } from '../../../services/invoice.service';
import { formatCurrency } from '../../../shared/utils/formatters';
import StatCard from '../../../shared/components/StatCard';

const InvoicesPage: React.FC = () => {
  const navigate = useNavigate();
  
  // State management
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [stats, setStats] = useState<InvoiceStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<InvoiceFilters>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

  // Load invoices
  const loadInvoices = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const currentFilters = { ...filters };
      if (searchTerm) currentFilters.search = searchTerm;
      
      const response = await invoiceService.getInvoices(currentFilters);
      setInvoices(response.results || []);
      
    } catch (err) {
      console.error('Error loading invoices:', err);
      setError(err instanceof Error ? err.message : 'Failed to load invoices');
    } finally {
      setLoading(false);
    }
  }, [filters, searchTerm]);

  // Load statistics
  const loadStats = useCallback(async () => {
    try {
      const statsData = await invoiceService.getInvoiceStats();
      setStats(statsData);
    } catch (err) {
      console.error('Error loading stats:', err);
    }
  }, []);

  // Effects
  useEffect(() => {
    loadInvoices();
  }, [loadInvoices]);

  useEffect(() => {
    loadStats();
  }, [loadStats]);

  // Handlers
  const handleCreateInvoice = () => {
    navigate('/dashboard/sales/invoices/create');
  };

  const handleViewInvoice = (invoice: Invoice) => {
    navigate(`/dashboard/sales/invoices/${invoice.id}`);
  };

  const handleEditInvoice = (invoice: Invoice) => {
    navigate(`/dashboard/sales/invoices/${invoice.id}/edit`);
  };

  const handleDeleteInvoice = async (invoice: Invoice) => {
    if (!window.confirm(`Are you sure you want to delete invoice ${invoice.invoice_number}?`)) {
        return;
      }
      
    try {
      await invoiceService.deleteInvoice(invoice.id!);
      await loadInvoices();
      await loadStats();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete invoice');
    }
  };

  const handleSendInvoice = async (invoice: Invoice) => {
    try {
      await invoiceService.sendInvoice(invoice.id!);
      await loadInvoices();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send invoice');
    }
  };

  const handleMarkAsPaid = async (invoice: Invoice) => {
    try {
      await invoiceService.markAsPaid(invoice.id!);
      await loadInvoices();
      await loadStats();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to mark invoice as paid');
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, invoice: Invoice) => {
    setMenuAnchor(event.currentTarget);
    setSelectedInvoice(invoice);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedInvoice(null);
  };

  const handleFilterChange = (field: keyof InvoiceFilters, value: any) => {
    setFilters(prev => ({ ...prev, [field]: value }));
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
  };

  // Helper functions
  const getStatusColor = (status: string) => {
    const colors = {
      draft: 'default',
      sent: 'info',
      paid: 'success',
      overdue: 'error',
      cancelled: 'default',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid': return <CheckCircleIcon fontSize="small" />;
      case 'overdue': return <WarningIcon fontSize="small" />;
      default: return undefined;
    }
  };

  if (loading && invoices.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={40} />
        <Typography variant="h6" sx={{ ml: 2 }}>Loading Invoices...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Invoices</Typography>
          <Button
            variant="contained"
            color="primary"
          onClick={handleCreateInvoice}
            startIcon={<AddIcon />}
          >
            Create Invoice
          </Button>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      {stats && (
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Revenue"
              value={formatCurrency(stats.total_revenue)}
              icon={<MoneyIcon />}
              color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Outstanding"
              value={formatCurrency(stats.outstanding_amount)}
              icon={<ReceiptIcon />}
              color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Invoices"
              value={stats.total_invoices}
              icon={<ReceiptIcon />}
              color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Overdue"
              value={stats.overdue_count}
            icon={<WarningIcon />}
            color="error"
          />
        </Grid>
      </Grid>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
            <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search invoices..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status || ''}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  label="Status"
                >
                  <MenuItem value="">All Statuses</MenuItem>
                  <MenuItem value="draft">Draft</MenuItem>
                  <MenuItem value="sent">Sent</MenuItem>
                  <MenuItem value="paid">Paid</MenuItem>
                  <MenuItem value="overdue">Overdue</MenuItem>
                  <MenuItem value="cancelled">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <DatePicker
                label="From Date"
                value={filters.date_from ? dayjs(filters.date_from) : null}
                onChange={(date) => handleFilterChange('date_from', date?.format('YYYY-MM-DD'))}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <DatePicker
                label="To Date"
                value={filters.date_to ? dayjs(filters.date_to) : null}
                onChange={(date) => handleFilterChange('date_to', date?.format('YYYY-MM-DD'))}
                slotProps={{ textField: { fullWidth: true } }}
              />
        </Grid>
            <Grid item xs={12} md={2}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  onClick={clearFilters}
                  startIcon={<FilterIcon />}
                >
                  Clear
                </Button>
                <Button
                  variant="outlined"
                  onClick={loadInvoices}
                  startIcon={<RefreshIcon />}
                >
                  Refresh
                </Button>
              </Box>
            </Grid>
          </Grid>
            </CardContent>
          </Card>

      {/* Invoices Table */}
      <Card>
        <CardContent>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Invoice #</TableCell>
                  <TableCell>Customer</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Due Date</TableCell>
                  <TableCell align="right">Amount</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {invoices.map((invoice) => (
                  <TableRow key={invoice.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {invoice.invoice_number}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      {invoice.customer_details?.display_name || `Customer ${invoice.customer}`}
                    </TableCell>
                    <TableCell>
                      {dayjs(invoice.invoice_date).format('MMM D, YYYY')}
                    </TableCell>
                    <TableCell>
                      {dayjs(invoice.due_date).format('MMM D, YYYY')}
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" fontWeight="medium">
                        {formatCurrency(invoice.total_amount)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                <Chip 
                  size="small"
                        label={invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                        color={getStatusColor(invoice.status) as any}
                        icon={getStatusIcon(invoice.status)}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuClick(e, invoice)}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
                {invoices.length === 0 && !loading && (
                  <TableRow>
                    <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                      <Typography color="text.secondary">
                        No invoices found. Create your first invoice to get started.
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => { handleViewInvoice(selectedInvoice!); handleMenuClose(); }}>
          <ListItemIcon><VisibilityIcon fontSize="small" /></ListItemIcon>
          <ListItemText>View</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => { handleEditInvoice(selectedInvoice!); handleMenuClose(); }}>
          <ListItemIcon><EditIcon fontSize="small" /></ListItemIcon>
          <ListItemText>Edit</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => { handleSendInvoice(selectedInvoice!); handleMenuClose(); }}>
          <ListItemIcon><EmailIcon fontSize="small" /></ListItemIcon>
          <ListItemText>Send</ListItemText>
        </MenuItem>
        {selectedInvoice?.status !== 'paid' && (
          <MenuItem onClick={() => { handleMarkAsPaid(selectedInvoice!); handleMenuClose(); }}>
            <ListItemIcon><PaymentIcon fontSize="small" /></ListItemIcon>
            <ListItemText>Mark as Paid</ListItemText>
          </MenuItem>
        )}
        <MenuItem onClick={() => { handleDeleteInvoice(selectedInvoice!); handleMenuClose(); }}>
          <ListItemIcon><DeleteIcon fontSize="small" /></ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default InvoicesPage;
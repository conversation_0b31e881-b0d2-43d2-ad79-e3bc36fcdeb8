// Invoice Context - State management for Invoice operations
// Provides invoice data and operations to components

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import {
  Invoice,
  InvoiceFormData,
  InvoiceFilters,
  InvoiceStats,
  CustomerOption,
  ProductOption,
  PaymentTermOption,
  SalesRepOption,
} from '../shared/types/invoice.types';
import invoiceService from '../services/invoice.service';

interface InvoiceContextType {
  // State
  invoices: Invoice[];
  currentInvoice: Invoice | null;
  loading: boolean;
  error: string | null;
  stats: InvoiceStats | null;
  
  // Lookup Data
  customers: CustomerOption[];
  products: ProductOption[];
  paymentTerms: PaymentTermOption[];
  salesReps: SalesRepOption[];
  
  // Actions
  loadInvoices: (filters?: InvoiceFilters) => Promise<void>;
  loadInvoice: (id: number) => Promise<void>;
  createInvoice: (data: InvoiceFormData) => Promise<Invoice | null>;
  updateInvoice: (id: number, data: Partial<InvoiceFormData>) => Promise<Invoice | null>;
  deleteInvoice: (id: number) => Promise<boolean>;
  updateInvoiceStatus: (id: number, status: string) => Promise<boolean>;
  
  // Inventory Operations
  checkInventoryAvailability: (id: number, warehouse_id?: number) => Promise<any>;
  issueInventory: (id: number, warehouse_id?: number) => Promise<any>;
  
  // Lookup Data Loading
  loadCustomers: (search?: string) => Promise<void>;
  loadProducts: (search?: string) => Promise<void>;
  loadPaymentTerms: () => Promise<void>;
  loadSalesReps: () => Promise<void>;
  
  // Utility Functions
  clearError: () => void;
  clearCurrentInvoice: () => void;
  refreshStats: () => Promise<void>;
}

const InvoiceContext = createContext<InvoiceContextType | undefined>(undefined);

interface InvoiceProviderProps {
  children: ReactNode;
}

export const InvoiceProvider: React.FC<InvoiceProviderProps> = ({ children }) => {
  // State
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [currentInvoice, setCurrentInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<InvoiceStats | null>(null);
  
  // Lookup Data State
  const [customers, setCustomers] = useState<CustomerOption[]>([]);
  const [products, setProducts] = useState<ProductOption[]>([]);
  const [paymentTerms, setPaymentTerms] = useState<PaymentTermOption[]>([]);
  const [salesReps, setSalesReps] = useState<SalesRepOption[]>([]);

  // Error Handling
  const handleError = useCallback((err: any) => {
    const message = err.response?.data?.message || err.message || 'An error occurred';
    setError(message);
    console.error('Invoice Context Error:', err);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Invoice Operations
  const loadInvoices = useCallback(async (filters?: InvoiceFilters) => {
    try {
      setLoading(true);
      setError(null);
      const response = await invoiceService.getInvoices(filters);
      setInvoices(response.results);
    } catch (err) {
      handleError(err);
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const loadInvoice = useCallback(async (id: number) => {
    try {
      setLoading(true);
      setError(null);
      const invoice = await invoiceService.getInvoice(id);
      setCurrentInvoice(invoice);
    } catch (err) {
      handleError(err);
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const createInvoice = useCallback(async (data: InvoiceFormData): Promise<Invoice | null> => {
    try {
      setLoading(true);
      setError(null);
      const response = await invoiceService.createInvoice(data);
      if (response.success) {
        setInvoices(prev => [response.invoice, ...prev]);
        return response.invoice;
      }
      return null;
    } catch (err) {
      handleError(err);
      return null;
    } finally {
      setLoading(false);
    }
  }, [handleError]);

  const updateInvoice = useCallback(async (id: number, data: Partial<InvoiceFormData>): Promise<Invoice | null> => {
    try {
      setLoading(true);
      setError(null);
      const response = await invoiceService.updateInvoice(id, data);
      if (response.success) {
        setInvoices(prev => prev.map(inv => inv.invoice_id === response.invoice.invoice_id ? response.invoice : inv));
        if (currentInvoice?.invoice_id === response.invoice.invoice_id) {
          setCurrentInvoice(response.invoice);
        }
        return response.invoice;
      }
      return null;
    } catch (err) {
      handleError(err);
      return null;
    } finally {
      setLoading(false);
    }
  }, [handleError, currentInvoice]);

  const deleteInvoice = useCallback(async (id: number): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      const response = await invoiceService.deleteInvoice(id);
      if (response.success) {
        setInvoices(prev => prev.filter(inv => inv.invoice_id !== id.toString()));
        if (currentInvoice?.invoice_id === id.toString()) {
          setCurrentInvoice(null);
        }
        return true;
      }
      return false;
    } catch (err) {
      handleError(err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [handleError, currentInvoice]);

  const updateInvoiceStatus = useCallback(async (id: number, status: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      const response = await invoiceService.updateInvoiceStatus(id, status);
      if (response.success) {
        setInvoices(prev => prev.map(inv => 
          inv.invoice_id === response.invoice.invoice_id ? response.invoice : inv
        ));
        if (currentInvoice?.invoice_id === response.invoice.invoice_id) {
          setCurrentInvoice(response.invoice);
        }
        return true;
      }
      return false;
    } catch (err) {
      handleError(err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [handleError, currentInvoice]);

  // Inventory Operations
  const checkInventoryAvailability = useCallback(async (id: number, warehouse_id?: number) => {
    try {
      setError(null);
      return await invoiceService.checkInventoryAvailability(id, warehouse_id);
    } catch (err) {
      handleError(err);
      return null;
    }
  }, [handleError]);

  const issueInventory = useCallback(async (id: number, warehouse_id?: number) => {
    try {
      setError(null);
      return await invoiceService.issueInventory(id, warehouse_id);
    } catch (err) {
      handleError(err);
      return null;
    }
  }, [handleError]);

  // Lookup Data Loading
  const loadCustomers = useCallback(async (search?: string) => {
    try {
      const customerData = await invoiceService.getCustomers(search);
      setCustomers(customerData);
    } catch (err) {
      handleError(err);
    }
  }, [handleError]);

  const loadProducts = useCallback(async (search?: string) => {
    try {
      const productData = await invoiceService.getProducts(search);
      setProducts(productData);
    } catch (err) {
      handleError(err);
    }
  }, [handleError]);

  const loadPaymentTerms = useCallback(async () => {
    try {
      const termsData = await invoiceService.getPaymentTerms();
      setPaymentTerms(termsData);
    } catch (err) {
      handleError(err);
    }
  }, [handleError]);

  const loadSalesReps = useCallback(async () => {
    try {
      const repsData = await invoiceService.getSalesReps();
      setSalesReps(repsData);
    } catch (err) {
      handleError(err);
    }
  }, [handleError]);

  // Utility Functions
  const clearCurrentInvoice = useCallback(() => {
    setCurrentInvoice(null);
  }, []);

  const refreshStats = useCallback(async () => {
    try {
      const statsData = await invoiceService.getInvoiceStats();
      setStats(statsData);
    } catch (err) {
      handleError(err);
    }
  }, [handleError]);

  const value: InvoiceContextType = {
    // State
    invoices,
    currentInvoice,
    loading,
    error,
    stats,
    
    // Lookup Data
    customers,
    products,
    paymentTerms,
    salesReps,
    
    // Actions
    loadInvoices,
    loadInvoice,
    createInvoice,
    updateInvoice,
    deleteInvoice,
    updateInvoiceStatus,
    
    // Inventory Operations
    checkInventoryAvailability,
    issueInventory,
    
    // Lookup Data Loading
    loadCustomers,
    loadProducts,
    loadPaymentTerms,
    loadSalesReps,
    
    // Utility Functions
    clearError,
    clearCurrentInvoice,
    refreshStats,
  };

  return (
    <InvoiceContext.Provider value={value}>
      {children}
    </InvoiceContext.Provider>
  );
};

export const useInvoice = (): InvoiceContextType => {
  const context = useContext(InvoiceContext);
  if (context === undefined) {
    throw new Error('useInvoice must be used within an InvoiceProvider');
  }
  return context;
};

export default InvoiceContext;

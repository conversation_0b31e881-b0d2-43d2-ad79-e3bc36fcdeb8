// Invoices Page - List and manage all invoices
// Built from scratch based on backend functionality

import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
} from '@mui/material';
import {
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Send as SendIcon,
  Cancel as CancelIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Receipt as ReceiptIcon,
  TrendingUp as TrendingUpIcon,
  AttachMoney as AttachMoneyIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import DataTable from '../../../shared/components/DataTable';
import StatCard from '../../../shared/components/StatCard';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { useInvoice } from '../../../contexts/InvoiceContext';
import { useCompany } from '../../../contexts/CompanyContext';
import { Invoice, INVOICE_STATUS_OPTIONS } from '../../../shared/types/invoice.types';

const InvoicesPage: React.FC = () => {
  const navigate = useNavigate();
  const { companyInfo } = useCompany();
  const {
    invoices,
    loading,
    error,
    stats,
    loadInvoices,
    deleteInvoice,
    updateInvoiceStatus,
    refreshStats,
    clearError,
  } = useInvoice();

  // Local State
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // Load data on component mount
  useEffect(() => {
    loadInvoices();
    refreshStats();
  }, [loadInvoices, refreshStats]);

  // Handle search and filters
  const handleSearch = useCallback(() => {
    const filters: any = {
      ...(searchTerm && { search: searchTerm }),
      ...(statusFilter && { status: statusFilter }),
    };
    loadInvoices(filters);
  }, [searchTerm, statusFilter, loadInvoices]);

  useEffect(() => {
    const timeoutId = setTimeout(handleSearch, 500);
    return () => clearTimeout(timeoutId);
  }, [handleSearch]);

  // Menu handlers
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, invoice: Invoice) => {
    setAnchorEl(event.currentTarget);
    setSelectedInvoice(invoice);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedInvoice(null);
  };

  // Action handlers
  const handleEdit = () => {
    if (selectedInvoice) {
      navigate(`/dashboard/sales/invoices/${selectedInvoice.invoice_id}/edit`);
    }
    handleMenuClose();
  };

  const handleView = () => {
    if (selectedInvoice) {
      navigate(`/dashboard/sales/invoices/${selectedInvoice.invoice_id}`);
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const handleStatusChange = (status: string) => {
    setNewStatus(status);
    setStatusDialogOpen(true);
    handleMenuClose();
  };

  const confirmDelete = async () => {
    if (selectedInvoice) {
      const success = await deleteInvoice(parseInt(selectedInvoice.invoice_id));
      if (success) {
        setSnackbar({ open: true, message: 'Invoice deleted successfully', severity: 'success' });
        refreshStats();
      } else {
        setSnackbar({ open: true, message: 'Failed to delete invoice', severity: 'error' });
      }
    }
    setDeleteDialogOpen(false);
    setSelectedInvoice(null);
  };

  const confirmStatusChange = async () => {
    if (selectedInvoice && newStatus) {
      const success = await updateInvoiceStatus(parseInt(selectedInvoice.invoice_id), newStatus);
      if (success) {
        setSnackbar({ open: true, message: 'Invoice status updated successfully', severity: 'success' });
        refreshStats();
      } else {
        setSnackbar({ open: true, message: 'Failed to update invoice status', severity: 'error' });
      }
    }
    setStatusDialogOpen(false);
    setSelectedInvoice(null);
    setNewStatus('');
  };

  // Table columns
  const columns = [
    {
      field: 'invoice_number',
      headerName: 'Invoice #',
      width: 150,
      renderCell: ({ row }: { row: Invoice }) => (
        <Typography
          variant="body2"
          sx={{ fontWeight: 600, color: 'primary.main', cursor: 'pointer' }}
          onClick={() => navigate(`/dashboard/sales/invoices/${row.invoice_id}`)}
        >
          {row.invoice_number}
        </Typography>
      ),
    },
    {
      field: 'customer_name',
      headerName: 'Customer',
      width: 200,
      valueGetter: ({ row }: { row: Invoice }) => row.customer_name || 'Unknown Customer',
    },
    {
      field: 'invoice_date',
      headerName: 'Date',
      width: 120,
      type: 'date' as const,
      valueGetter: ({ row }: { row: Invoice }) => formatDate(row.invoice_date),
    },
    {
      field: 'due_date',
      headerName: 'Due Date',
      width: 120,
      type: 'date' as const,
      valueGetter: ({ row }: { row: Invoice }) => formatDate(row.due_date),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: ({ row }: { row: Invoice }) => {
        const statusOption = INVOICE_STATUS_OPTIONS.find(opt => opt.value === row.status);
        return (
          <Chip
            label={statusOption?.label || row.status}
            color={statusOption?.color as any}
            size="small"
          />
        );
      },
    },
    {
      field: 'total_amount',
      headerName: 'Amount',
      width: 130,
      type: 'currency' as const,
      renderCell: ({ row }: { row: Invoice }) => (
        <Typography variant="body2" sx={{ fontWeight: 600 }}>
          {formatCurrency(parseFloat(row.total_amount), companyInfo?.functionalCurrency)}
        </Typography>
      ),
    },
    {
      field: 'balance_due',
      headerName: 'Balance',
      width: 130,
      type: 'currency' as const,
      renderCell: ({ row }: { row: Invoice }) => (
        <Typography
          variant="body2"
          sx={{
            fontWeight: 600,
            color: parseFloat(row.balance_due) > 0 ? 'error.main' : 'success.main'
          }}
        >
          {formatCurrency(parseFloat(row.balance_due), companyInfo?.functionalCurrency)}
        </Typography>
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 100,
      sortable: false,
      renderCell: ({ row }: { row: Invoice }) => (
        <IconButton
          size="small"
          onClick={(e) => handleMenuOpen(e, row)}
        >
          <MoreVertIcon />
        </IconButton>
      ),
    },
  ];

  return (
    <PageContainer>
      <PageHeader>
        <Typography variant="h4" component="h1">
          Invoices
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/dashboard/sales/invoices/create')}
          sx={{
            background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
            '&:hover': {
              background: 'linear-gradient(45deg, #1565c0 30%, #1976d2 90%)',
            }
          }}
        >
          Create Invoice
        </Button>
      </PageHeader>

      {/* Statistics Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Invoices"
              value={stats.total_invoices.toString()}
              icon={<ReceiptIcon />}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Outstanding"
              value={formatCurrency(parseFloat(stats.outstanding_amount), companyInfo?.functionalCurrency)}
              icon={<AttachMoneyIcon />}
              color="warning"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Overdue"
              value={stats.overdue_invoices.toString()}
              icon={<ScheduleIcon />}
              color="error"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Paid This Month"
              value={formatCurrency(parseFloat(stats.paid_amount), companyInfo?.functionalCurrency)}
              icon={<TrendingUpIcon />}
              color="success"
            />
          </Grid>
        </Grid>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search invoices..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  label="Status"
                >
                  <MenuItem value="">All Statuses</MenuItem>
                  {INVOICE_STATUS_OPTIONS.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('');
                  loadInvoices();
                }}
              >
                Reset
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Invoices Table */}
      <Card>
        <DataTable
          rows={invoices}
          columns={columns}
          loading={loading}
          noDataText="No invoices found"
        />
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleView}>
          <ReceiptIcon sx={{ mr: 1 }} />
          View
        </MenuItem>
        <MenuItem onClick={handleEdit}>
          <EditIcon sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={() => handleStatusChange('sent')}>
          <SendIcon sx={{ mr: 1 }} />
          Send
        </MenuItem>
        <MenuItem onClick={() => handleStatusChange('cancelled')}>
          <CancelIcon sx={{ mr: 1 }} />
          Cancel
        </MenuItem>
        <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Invoice</DialogTitle>
        <DialogContent>
          Are you sure you want to delete invoice {selectedInvoice?.invoice_number}?
          This action cannot be undone.
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Status Change Dialog */}
      <Dialog open={statusDialogOpen} onClose={() => setStatusDialogOpen(false)}>
        <DialogTitle>Change Invoice Status</DialogTitle>
        <DialogContent>
          Change status of invoice {selectedInvoice?.invoice_number} to{' '}
          {INVOICE_STATUS_OPTIONS.find(opt => opt.value === newStatus)?.label}?
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStatusDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmStatusChange} color="primary" variant="contained">
            Update Status
          </Button>
        </DialogActions>
      </Dialog>

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mt: 2 }} onClose={clearError}>
          {error}
        </Alert>
      )}

      {/* Success/Error Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </PageContainer>
  );
};

export default InvoicesPage;

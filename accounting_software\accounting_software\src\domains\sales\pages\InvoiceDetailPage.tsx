// Invoice Detail Page - View and manage individual invoice
// Built from scratch based on backend functionality

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Divider,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Menu,
  MenuItem,
} from '@mui/material';
import {
  Edit as EditIcon,
  Print as PrintIcon,
  Email as EmailIcon,
  MoreVert as MoreVertIcon,
  Send as SendIcon,
  Cancel as CancelIcon,
  Receipt as ReceiptIcon,
  Inventory as InventoryIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import { useInvoice } from '../../../contexts/InvoiceContext';
import { useCompany } from '../../../contexts/CompanyContext';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { INVOICE_STATUS_OPTIONS } from '../../../shared/types/invoice.types';

const InvoiceDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { companyInfo } = useCompany();
  const {
    currentInvoice,
    loading,
    error,
    loadInvoice,
    updateInvoiceStatus,
    checkInventoryAvailability,
    issueInventory,
    clearError,
  } = useInvoice();

  // Local State
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [inventoryCheck, setInventoryCheck] = useState<any>(null);
  const [inventoryLoading, setInventoryLoading] = useState(false);
  const [issueDialogOpen, setIssueDialogOpen] = useState(false);

  // Load invoice data
  useEffect(() => {
    if (id) {
      loadInvoice(parseInt(id));
    }
  }, [id, loadInvoice]);

  // Check inventory on load
  useEffect(() => {
    if (currentInvoice && currentInvoice.status === 'draft') {
      handleInventoryCheck();
    }
  }, [currentInvoice]);

  const handleInventoryCheck = async () => {
    if (!id) return;
    
    setInventoryLoading(true);
    try {
      const result = await checkInventoryAvailability(parseInt(id));
      setInventoryCheck(result);
    } catch (err) {
      console.error('Error checking inventory:', err);
    } finally {
      setInventoryLoading(false);
    }
  };

  const handleIssueInventory = async () => {
    if (!id) return;
    
    try {
      const result = await issueInventory(parseInt(id));
      if (result?.success) {
        setIssueDialogOpen(false);
        // Reload invoice to get updated status
        loadInvoice(parseInt(id));
        // Refresh inventory check
        handleInventoryCheck();
      }
    } catch (err) {
      console.error('Error issuing inventory:', err);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleStatusChange = (status: string) => {
    setNewStatus(status);
    setStatusDialogOpen(true);
    handleMenuClose();
  };

  const confirmStatusChange = async () => {
    if (currentInvoice && newStatus) {
      const success = await updateInvoiceStatus(parseInt(currentInvoice.invoice_id), newStatus);
      if (success) {
        loadInvoice(parseInt(currentInvoice.invoice_id));
      }
    }
    setStatusDialogOpen(false);
    setNewStatus('');
  };

  const handleEdit = () => {
    navigate(`/dashboard/sales/invoices/${id}/edit`);
    handleMenuClose();
  };

  const handlePrint = () => {
    window.print();
    handleMenuClose();
  };

  const handleEmail = () => {
    // TODO: Implement email functionality
    handleMenuClose();
  };

  if (loading) {
    return (
      <PageContainer>
        <Typography>Loading invoice...</Typography>
      </PageContainer>
    );
  }

  if (!currentInvoice) {
    return (
      <PageContainer>
        <Alert severity="error">Invoice not found</Alert>
      </PageContainer>
    );
  }

  const statusOption = INVOICE_STATUS_OPTIONS.find(opt => opt.value === currentInvoice.status);

  return (
    <PageContainer>
      <PageHeader>
        <Typography variant="h4" component="h1">
          Invoice {currentInvoice.invoice_number}
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={handleEdit}
          >
            Edit
          </Button>
          <Button
            variant="outlined"
            startIcon={<PrintIcon />}
            onClick={handlePrint}
          >
            Print
          </Button>
          <IconButton onClick={handleMenuOpen}>
            <MoreVertIcon />
          </IconButton>
        </Box>
      </PageHeader>

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={clearError}>
          {error}
        </Alert>
      )}

      {/* Invoice Header */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Bill To:
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 600 }}>
                {currentInvoice.customer_name}
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ textAlign: 'right' }}>
                <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
                  {currentInvoice.invoice_number}
                </Typography>
                <Chip
                  label={statusOption?.label || currentInvoice.status}
                  color={statusOption?.color as any}
                  sx={{ mb: 2 }}
                />
                <Typography variant="body2">
                  <strong>Date:</strong> {formatDate(currentInvoice.invoice_date)}
                </Typography>
                <Typography variant="body2">
                  <strong>Due Date:</strong> {formatDate(currentInvoice.due_date)}
                </Typography>
                {currentInvoice.po_number && (
                  <Typography variant="body2">
                    <strong>PO Number:</strong> {currentInvoice.po_number}
                  </Typography>
                )}
                {currentInvoice.sales_rep_name && (
                  <Typography variant="body2">
                    <strong>Sales Rep:</strong> {currentInvoice.sales_rep_name}
                  </Typography>
                )}
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Inventory Status */}
      {inventoryCheck && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="h6">
                Inventory Status
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  startIcon={<InventoryIcon />}
                  onClick={handleInventoryCheck}
                  disabled={inventoryLoading}
                  size="small"
                >
                  Refresh
                </Button>
                {inventoryCheck.all_available && currentInvoice.status === 'draft' && (
                  <Button
                    variant="contained"
                    startIcon={<CheckCircleIcon />}
                    onClick={() => setIssueDialogOpen(true)}
                    color="success"
                    size="small"
                  >
                    Issue Inventory
                  </Button>
                )}
              </Box>
            </Box>
            
            {inventoryCheck.all_available ? (
              <Alert severity="success" icon={<CheckCircleIcon />}>
                All items are available in stock
              </Alert>
            ) : (
              <Alert severity="warning" icon={<WarningIcon />}>
                Some items have insufficient stock
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Line Items */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Line Items
          </Typography>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Product</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell align="right">Quantity</TableCell>
                  <TableCell align="right">Unit Price</TableCell>
                  <TableCell align="right">Tax</TableCell>
                  <TableCell align="right">Total</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {currentInvoice.line_items.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {item.product_code}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {item.product_name}
                      </Typography>
                    </TableCell>
                    <TableCell>{item.description}</TableCell>
                    <TableCell align="right">{item.quantity}</TableCell>
                    <TableCell align="right">
                      {formatCurrency(parseFloat(item.unit_price), companyInfo?.functionalCurrency)}
                    </TableCell>
                    <TableCell align="right">
                      {item.tax_amount ? formatCurrency(parseFloat(item.tax_amount), companyInfo?.functionalCurrency) : '-'}
                    </TableCell>
                    <TableCell align="right">
                      {formatCurrency(parseFloat(item.line_total), companyInfo?.functionalCurrency)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Totals */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container justifyContent="flex-end">
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'right' }}>
                <Typography variant="body1">
                  Subtotal: {formatCurrency(parseFloat(currentInvoice.subtotal_amount), companyInfo?.functionalCurrency)}
                </Typography>
                <Typography variant="body1">
                  Tax: {formatCurrency(parseFloat(currentInvoice.tax_amount), companyInfo?.functionalCurrency)}
                </Typography>
                {parseFloat(currentInvoice.discount_amount) > 0 && (
                  <Typography variant="body1">
                    Discount: {formatCurrency(parseFloat(currentInvoice.discount_amount), companyInfo?.functionalCurrency)}
                  </Typography>
                )}
                {parseFloat(currentInvoice.shipping_amount) > 0 && (
                  <Typography variant="body1">
                    Shipping: {formatCurrency(parseFloat(currentInvoice.shipping_amount), companyInfo?.functionalCurrency)}
                  </Typography>
                )}
                <Divider sx={{ my: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Total: {formatCurrency(parseFloat(currentInvoice.total_amount), companyInfo?.functionalCurrency)}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Paid: {formatCurrency(parseFloat(currentInvoice.amount_paid), companyInfo?.functionalCurrency)}
                </Typography>
                <Typography variant="h6" sx={{ fontWeight: 600, color: 'error.main' }}>
                  Balance Due: {formatCurrency(parseFloat(currentInvoice.balance_due), companyInfo?.functionalCurrency)}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Additional Information */}
      {(currentInvoice.memo || currentInvoice.terms_conditions || currentInvoice.footer_text) && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Additional Information
            </Typography>
            {currentInvoice.memo && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Memo:
                </Typography>
                <Typography variant="body2">
                  {currentInvoice.memo}
                </Typography>
              </Box>
            )}
            {currentInvoice.terms_conditions && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Terms & Conditions:
                </Typography>
                <Typography variant="body2">
                  {currentInvoice.terms_conditions}
                </Typography>
              </Box>
            )}
            {currentInvoice.footer_text && (
              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Footer:
                </Typography>
                <Typography variant="body2">
                  {currentInvoice.footer_text}
                </Typography>
              </Box>
            )}
          </CardContent>
        </Card>
      )}

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleStatusChange('sent')}>
          <SendIcon sx={{ mr: 1 }} />
          Send Invoice
        </MenuItem>
        <MenuItem onClick={handleEmail}>
          <EmailIcon sx={{ mr: 1 }} />
          Email Invoice
        </MenuItem>
        <MenuItem onClick={() => handleStatusChange('cancelled')}>
          <CancelIcon sx={{ mr: 1 }} />
          Cancel Invoice
        </MenuItem>
      </Menu>

      {/* Status Change Dialog */}
      <Dialog open={statusDialogOpen} onClose={() => setStatusDialogOpen(false)}>
        <DialogTitle>Change Invoice Status</DialogTitle>
        <DialogContent>
          Change status of invoice {currentInvoice.invoice_number} to{' '}
          {INVOICE_STATUS_OPTIONS.find(opt => opt.value === newStatus)?.label}?
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStatusDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmStatusChange} color="primary" variant="contained">
            Update Status
          </Button>
        </DialogActions>
      </Dialog>

      {/* Issue Inventory Dialog */}
      <Dialog open={issueDialogOpen} onClose={() => setIssueDialogOpen(false)}>
        <DialogTitle>Issue Inventory</DialogTitle>
        <DialogContent>
          This will reduce inventory quantities for all line items in this invoice.
          This action cannot be undone. Continue?
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIssueDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleIssueInventory} color="primary" variant="contained">
            Issue Inventory
          </Button>
        </DialogActions>
      </Dialog>
    </PageContainer>
  );
};

export default InvoiceDetailPage;

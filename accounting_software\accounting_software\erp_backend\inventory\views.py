"""
Enterprise Inventory Views
Following our established patterns with comprehensive functionality
"""

from django.db.models import Q, Sum, Count, F, Case, When, DecimalField
from django.utils import timezone
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend

from .models import (
    Warehouse, StockLevel, StockTransaction, StockAdjustment, StockAdjustmentItem,
    StockTransfer, StockTransferItem, StockValuation, StockValuationItem
)
from .serializers import (
    WarehouseSerializer, StockLevelSerializer, StockTransactionSerializer,
    StockAdjustmentSerializer, StockAdjustmentCreateSerializer,
    StockTransferSerializer, StockTransferCreateSerializer,
    StockValuationSerializer, WarehouseSummarySerializer, InventorySummarySerializer
)
from .pagination import InventoryPagination


class WarehouseViewSet(viewsets.ModelViewSet):
    """Warehouse management with location tracking"""
    queryset = Warehouse.objects.all()
    serializer_class = WarehouseSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['warehouse_type', 'is_active', 'allow_negative_stock']
    search_fields = ['code', 'name', 'city', 'state']
    ordering_fields = ['code', 'name', 'created_at']
    ordering = ['code']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get warehouse summary statistics"""
        warehouses = self.get_queryset().filter(is_active=True)
        
        summary_data = []
        for warehouse in warehouses:
            stock_levels = StockLevel.objects.filter(warehouse=warehouse)
            
            summary = {
                'warehouse_id': warehouse.warehouse_id,
                'warehouse_code': warehouse.code,
                'warehouse_name': warehouse.name,
                'total_products': stock_levels.count(),
                'total_quantity': stock_levels.aggregate(
                    total=Sum('quantity_on_hand')
                )['total'] or 0,
                'total_value': sum(
                    level.stock_value_average for level in stock_levels
                ),
                'low_stock_items': stock_levels.filter(
                    quantity_on_hand__lte=F('reorder_point')
                ).count(),
                'out_of_stock_items': stock_levels.filter(
                    quantity_on_hand__lte=0
                ).count(),
            }
            summary_data.append(summary)
        
        serializer = WarehouseSummarySerializer(summary_data, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def stock_levels(self, request, pk=None):
        """Get stock levels for a specific warehouse"""
        warehouse = self.get_object()
        stock_levels = StockLevel.objects.filter(warehouse=warehouse)
        
        # Apply filters
        product_code = request.query_params.get('product_code')
        low_stock = request.query_params.get('low_stock')
        out_of_stock = request.query_params.get('out_of_stock')
        
        if product_code:
            stock_levels = stock_levels.filter(product__code__icontains=product_code)
        if low_stock == 'true':
            stock_levels = stock_levels.filter(quantity_on_hand__lte=F('reorder_point'))
        if out_of_stock == 'true':
            stock_levels = stock_levels.filter(quantity_on_hand__lte=0)
        
        serializer = StockLevelSerializer(stock_levels, many=True)
        return Response(serializer.data)


class StockLevelViewSet(viewsets.ModelViewSet):
    """Stock level management with real-time tracking"""
    queryset = StockLevel.objects.select_related('product', 'warehouse').all()
    serializer_class = StockLevelSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['warehouse', 'product']
    search_fields = ['product__code', 'product__name', 'warehouse__code', 'warehouse__name']
    ordering_fields = ['product__code', 'warehouse__code', 'quantity_on_hand', 'last_updated']
    ordering = ['product__code', 'warehouse__code']
    pagination_class = InventoryPagination
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter options
        low_stock = self.request.query_params.get('low_stock')
        out_of_stock = self.request.query_params.get('out_of_stock')
        warehouse_type = self.request.query_params.get('warehouse_type')
        
        if low_stock == 'true':
            queryset = queryset.filter(quantity_on_hand__lte=F('reorder_point'))
        if out_of_stock == 'true':
            queryset = queryset.filter(quantity_on_hand__lte=0)
        if warehouse_type:
            queryset = queryset.filter(warehouse__warehouse_type=warehouse_type)
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get overall inventory summary"""
        queryset = self.get_queryset()
        
        summary = {
            'total_warehouses': Warehouse.objects.filter(is_active=True).count(),
            'total_products': queryset.values('product').distinct().count(),
            'total_quantity': queryset.aggregate(
                total=Sum('quantity_on_hand')
            )['total'] or 0,
            'total_value': sum(
                level.stock_value_average for level in queryset
            ),
            'low_stock_items': queryset.filter(
                quantity_on_hand__lte=F('reorder_point')
            ).count(),
            'out_of_stock_items': queryset.filter(
                quantity_on_hand__lte=0
            ).count(),
            'pending_transfers': StockTransfer.objects.filter(
                status='IN_TRANSIT'
            ).count(),
            'pending_adjustments': StockAdjustment.objects.filter(
                status='DRAFT'
            ).count(),
        }
        
        serializer = InventorySummarySerializer(summary)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def low_stock(self, request):
        """Get items below reorder point"""
        low_stock_items = self.get_queryset().filter(
            quantity_on_hand__lte=F('reorder_point')
        ).order_by('quantity_on_hand')
        
        serializer = self.get_serializer(low_stock_items, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def out_of_stock(self, request):
        """Get out of stock items"""
        out_of_stock_items = self.get_queryset().filter(
            quantity_on_hand__lte=0
        ).order_by('quantity_on_hand')
        
        serializer = self.get_serializer(out_of_stock_items, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def reserve_stock(self, request, pk=None):
        """Reserve stock for sales order"""
        stock_level = self.get_object()
        quantity = request.data.get('quantity', 0)
        
        if stock_level.reserve_stock(quantity):
            return Response({'message': 'Stock reserved successfully'})
        else:
            return Response(
                {'error': 'Insufficient stock available'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    def release_reservation(self, request, pk=None):
        """Release reserved stock"""
        stock_level = self.get_object()
        quantity = request.data.get('quantity', 0)
        
        stock_level.release_reservation(quantity)
        return Response({'message': 'Reservation released successfully'})


class StockTransactionViewSet(viewsets.ModelViewSet):
    """Stock transaction tracking with complete audit trail"""
    queryset = StockTransaction.objects.select_related('product', 'warehouse', 'created_by').all()
    serializer_class = StockTransactionSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['transaction_type', 'warehouse', 'product', 'reference_type']
    search_fields = ['product__code', 'product__name', 'reference_number', 'description']
    ordering_fields = ['transaction_date', 'created_at', 'product__code']
    ordering = ['-transaction_date', '-created_at']
    pagination_class = InventoryPagination
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Date range filtering
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(transaction_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(transaction_date__lte=end_date)
        
        return queryset
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=False, methods=['get'])
    def by_product(self, request):
        """Get transactions for a specific product"""
        product_id = request.query_params.get('product_id')
        if not product_id:
            return Response(
                {'error': 'product_id parameter required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        transactions = self.get_queryset().filter(product_id=product_id)
        serializer = self.get_serializer(transactions, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def by_warehouse(self, request):
        """Get transactions for a specific warehouse"""
        warehouse_id = request.query_params.get('warehouse_id')
        if not warehouse_id:
            return Response(
                {'error': 'warehouse_id parameter required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        transactions = self.get_queryset().filter(warehouse_id=warehouse_id)
        serializer = self.get_serializer(transactions, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get transaction statistics"""
        queryset = self.get_queryset()
        
        stats = {
            'total_transactions': queryset.count(),
            'receipts': queryset.filter(transaction_type='RECEIPT').count(),
            'issues': queryset.filter(transaction_type='ISSUE').count(),
            'adjustments': queryset.filter(transaction_type='ADJUSTMENT').count(),
            'transfers': queryset.filter(
                transaction_type__in=['TRANSFER_IN', 'TRANSFER_OUT']
            ).count(),
            'total_value': queryset.aggregate(
                total=Sum('total_cost')
            )['total'] or 0,
        }
        
        return Response(stats)


class StockAdjustmentViewSet(viewsets.ModelViewSet):
    """Stock adjustment management with posting workflow"""
    queryset = StockAdjustment.objects.select_related('warehouse', 'created_by', 'posted_by').all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'reason', 'warehouse']
    search_fields = ['adjustment_number', 'description']
    ordering_fields = ['adjustment_date', 'created_at', 'adjustment_number']
    ordering = ['-adjustment_date', '-created_at']
    pagination_class = InventoryPagination

    def get_serializer_class(self):
        if self.action == 'create':
            return StockAdjustmentCreateSerializer
        return StockAdjustmentSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def post_adjustment(self, request, pk=None):
        """Post the adjustment and create stock transactions"""
        adjustment = self.get_object()

        if adjustment.post_adjustment(request.user):
            return Response({'message': 'Adjustment posted successfully'})
        else:
            return Response(
                {'error': 'Cannot post adjustment in current status'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def pending(self, request):
        """Get pending adjustments"""
        pending = self.get_queryset().filter(status='DRAFT')
        serializer = self.get_serializer(pending, many=True)
        return Response(serializer.data)


class StockTransferViewSet(viewsets.ModelViewSet):
    """Stock transfer management with shipping workflow"""
    queryset = StockTransfer.objects.select_related(
        'from_warehouse', 'to_warehouse', 'created_by', 'shipped_by', 'received_by'
    ).all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'from_warehouse', 'to_warehouse']
    search_fields = ['transfer_number', 'description']
    ordering_fields = ['transfer_date', 'created_at', 'transfer_number']
    ordering = ['-transfer_date', '-created_at']
    pagination_class = InventoryPagination

    def get_serializer_class(self):
        if self.action == 'create':
            return StockTransferCreateSerializer
        return StockTransferSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def ship_transfer(self, request, pk=None):
        """Ship the transfer (create outbound transactions)"""
        transfer = self.get_object()

        if transfer.ship_transfer(request.user):
            return Response({'message': 'Transfer shipped successfully'})
        else:
            return Response(
                {'error': 'Cannot ship transfer in current status'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def receive_transfer(self, request, pk=None):
        """Receive the transfer (create inbound transactions)"""
        transfer = self.get_object()

        if transfer.receive_transfer(request.user):
            return Response({'message': 'Transfer received successfully'})
        else:
            return Response(
                {'error': 'Cannot receive transfer in current status'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def in_transit(self, request):
        """Get transfers in transit"""
        in_transit = self.get_queryset().filter(status='IN_TRANSIT')
        serializer = self.get_serializer(in_transit, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def pending(self, request):
        """Get pending transfers"""
        pending = self.get_queryset().filter(status='DRAFT')
        serializer = self.get_serializer(pending, many=True)
        return Response(serializer.data)


class StockValuationViewSet(viewsets.ModelViewSet):
    """Stock valuation management with multiple costing methods"""
    queryset = StockValuation.objects.select_related('warehouse', 'created_by', 'finalized_by').all()
    serializer_class = StockValuationSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'method', 'warehouse']
    search_fields = ['warehouse__code', 'warehouse__name']
    ordering_fields = ['valuation_date', 'created_at']
    ordering = ['-valuation_date', '-created_at']
    pagination_class = InventoryPagination

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def finalize_valuation(self, request, pk=None):
        """Finalize the valuation"""
        valuation = self.get_object()

        if valuation.status == 'DRAFT':
            valuation.status = 'FINALIZED'
            valuation.finalized_at = timezone.now()
            valuation.finalized_by = request.user
            valuation.save()
            return Response({'message': 'Valuation finalized successfully'})
        else:
            return Response(
                {'error': 'Cannot finalize valuation in current status'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['post'])
    def generate_valuation(self, request):
        """Generate stock valuation for a warehouse"""
        warehouse_id = request.data.get('warehouse_id')
        method = request.data.get('method', 'AVERAGE')
        valuation_date = request.data.get('valuation_date')

        if not warehouse_id:
            return Response(
                {'error': 'warehouse_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            warehouse = Warehouse.objects.get(warehouse_id=warehouse_id)
        except Warehouse.DoesNotExist:
            return Response(
                {'error': 'Warehouse not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Create valuation
        valuation = StockValuation.objects.create(
            valuation_date=valuation_date or timezone.now().date(),
            warehouse=warehouse,
            method=method,
            created_by=request.user
        )

        # Generate valuation items
        stock_levels = StockLevel.objects.filter(
            warehouse=warehouse,
            quantity_on_hand__gt=0
        )

        total_items = 0
        total_quantity = 0
        total_value = 0

        for stock_level in stock_levels:
            if method == 'AVERAGE':
                unit_cost = stock_level.average_cost
            elif method == 'STANDARD':
                unit_cost = stock_level.standard_cost
            else:  # FIFO, LIFO - simplified to last cost for now
                unit_cost = stock_level.last_cost

            StockValuationItem.objects.create(
                valuation=valuation,
                product=stock_level.product,
                quantity=stock_level.quantity_on_hand,
                unit_cost=unit_cost
            )

            total_items += 1
            total_quantity += stock_level.quantity_on_hand
            total_value += stock_level.quantity_on_hand * unit_cost

        # Update valuation totals
        valuation.total_items = total_items
        valuation.total_quantity = total_quantity
        valuation.total_value = total_value
        valuation.save()

        serializer = self.get_serializer(valuation)
        return Response(serializer.data)

// Invoice Form Component - Create and edit invoices
// Built from scratch based on backend functionality with security at backend level

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  Autocomplete,
  Button,
  Box,
  Divider,
  Alert,
  Chip,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  SaveAlt as SaveAltIcon,
  Cancel as CancelIcon,
  ExpandMore as ExpandMoreIcon,
  Inventory as InventoryIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useNavigate } from 'react-router-dom';
import { useInvoice } from '../../../contexts/InvoiceContext';
import { useCompany } from '../../../contexts/CompanyContext';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';
import {
  InvoiceFormData,
  InvoiceLineItemFormData,
  CustomerOption,
  ProductOption,
  PaymentTermOption,
  SalesRepOption,
} from '../../../shared/types/invoice.types';
import {
  JournalLineTable,
  FormattedCurrencyInput,
  QuantityInput,
} from '../../../shared/components';
import { formatCurrency } from '../../../shared/utils/formatters';

interface InvoiceFormProps {
  invoiceId?: string;
  onClose?: () => void;
  onSave?: () => void;
}

// Validation Schema
const validationSchema = Yup.object({
  customer: Yup.number().required('Customer is required'),
  invoice_date: Yup.string().required('Invoice date is required'),
  payment_terms: Yup.number().required('Payment terms are required'),
  line_items: Yup.array()
    .of(
      Yup.object({
        product: Yup.number().required('Product is required'),
        quantity: Yup.string()
          .required('Quantity is required')
          .test('positive', 'Quantity must be positive', (value) => 
            parseFloat(value || '0') > 0
          ),
      })
    )
    .min(1, 'At least one line item is required'),
});

const InvoiceForm: React.FC<InvoiceFormProps> = ({ invoiceId, onClose, onSave }) => {
  const navigate = useNavigate();
  const { companyInfo } = useCompany();
  const { currencyInfo } = useCurrencyInfo();
  const {
    currentInvoice,
    loading,
    error,
    customers,
    products,
    paymentTerms,
    salesReps,
    loadInvoice,
    createInvoice,
    updateInvoice,
    loadCustomers,
    loadProducts,
    loadPaymentTerms,
    loadSalesReps,
    checkInventoryAvailability,
    clearError,
  } = useInvoice();

  // Local State
  const [inventoryCheck, setInventoryCheck] = useState<any>(null);
  const [inventoryLoading, setInventoryLoading] = useState(false);
  const [expanded, setExpanded] = useState<string>('panel1');

  // Load data on component mount
  useEffect(() => {
    loadCustomers();
    loadProducts();
    loadPaymentTerms();
    loadSalesReps();
    
    if (invoiceId) {
      loadInvoice(parseInt(invoiceId));
    }
  }, [invoiceId, loadCustomers, loadProducts, loadPaymentTerms, loadSalesReps, loadInvoice]);

  // Initialize form with current invoice data or defaults
  const initialValues: InvoiceFormData = {
    customer: currentInvoice?.customer || null,
    invoice_date: currentInvoice?.invoice_date || dayjs().format('YYYY-MM-DD'),
    payment_terms: currentInvoice?.payment_terms || null,
    po_number: currentInvoice?.po_number || '',
    sales_rep: currentInvoice?.sales_rep || null,
    discount_amount: currentInvoice?.discount_amount || '0.00',
    shipping_amount: currentInvoice?.shipping_amount || '0.00',
    memo: currentInvoice?.memo || '',
    terms_conditions: currentInvoice?.terms_conditions || '',
    footer_text: currentInvoice?.footer_text || '',
    line_items: currentInvoice?.line_items?.map(item => ({
      product: item.product,
      description: item.description || '',
      quantity: item.quantity,
      unit_price: item.unit_price,
      tax_rate: item.tax_rate || '',
      discount_percentage: item.discount_percentage || '',
    })) || [{
      product: null,
      description: '',
      quantity: '1',
      unit_price: '0.00',
      tax_rate: '',
      discount_percentage: '',
    }],
  };

  const formik = useFormik({
    initialValues,
    validationSchema,
    enableReinitialize: true,
    onSubmit: async (values) => {
      try {
        if (invoiceId) {
          const result = await updateInvoice(parseInt(invoiceId), values);
          if (result) {
            onSave?.();
            navigate('/dashboard/sales/invoices');
          }
        } else {
          const result = await createInvoice(values);
          if (result) {
            onSave?.();
            navigate('/dashboard/sales/invoices');
          }
        }
      } catch (err) {
        console.error('Error saving invoice:', err);
      }
    },
  });

  // Handle line item changes
  const handleLineItemChange = useCallback((index: number, field: string, value: any) => {
    const newLineItems = [...formik.values.line_items];
    newLineItems[index] = { ...newLineItems[index], [field]: value };
    
    // Auto-fill price when product is selected
    if (field === 'product' && value) {
      const product = products.find(p => p.id === value);
      if (product) {
        newLineItems[index].unit_price = product.sale_price;
        newLineItems[index].tax_rate = product.tax_rate || '';
      }
    }
    
    formik.setFieldValue('line_items', newLineItems);
  }, [formik, products]);

  const handleAddLineItem = useCallback(() => {
    const newLineItems = [...formik.values.line_items, {
      product: null,
      description: '',
      quantity: '1',
      unit_price: '0.00',
      tax_rate: '',
      discount_percentage: '',
    }];
    formik.setFieldValue('line_items', newLineItems);
  }, [formik]);

  const handleRemoveLineItem = useCallback((index: number) => {
    if (formik.values.line_items.length > 1) {
      const newLineItems = formik.values.line_items.filter((_, i) => i !== index);
      formik.setFieldValue('line_items', newLineItems);
    }
  }, [formik]);

  // Check inventory availability
  const handleInventoryCheck = useCallback(async () => {
    if (!invoiceId) return;
    
    setInventoryLoading(true);
    try {
      const result = await checkInventoryAvailability(parseInt(invoiceId));
      setInventoryCheck(result);
    } catch (err) {
      console.error('Error checking inventory:', err);
    } finally {
      setInventoryLoading(false);
    }
  }, [invoiceId, checkInventoryAvailability]);

  // Calculate totals
  const calculateTotals = useCallback(() => {
    const lineItems = formik.values.line_items;
    let subtotal = 0;
    let totalTax = 0;
    
    lineItems.forEach(item => {
      if (item.product && item.quantity && item.unit_price) {
        const quantity = parseFloat(item.quantity);
        const unitPrice = parseFloat(item.unit_price);
        const lineTotal = quantity * unitPrice;
        
        // Apply discount
        const discountPercent = parseFloat(item.discount_percentage || '0');
        const discountAmount = (lineTotal * discountPercent) / 100;
        const lineSubtotal = lineTotal - discountAmount;
        
        subtotal += lineSubtotal;
        
        // Calculate tax
        const taxRate = parseFloat(item.tax_rate || '0');
        const taxAmount = (lineSubtotal * taxRate) / 100;
        totalTax += taxAmount;
      }
    });
    
    const discountAmount = parseFloat(formik.values.discount_amount || '0');
    const shippingAmount = parseFloat(formik.values.shipping_amount || '0');
    const total = subtotal - discountAmount + shippingAmount + totalTax;
    
    return {
      subtotal,
      totalTax,
      discountAmount,
      shippingAmount,
      total,
    };
  }, [formik.values]);

  const totals = calculateTotals();

  // Handle accordion expansion
  const handleAccordionChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded(isExpanded ? panel : '');
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box component="form" onSubmit={formik.handleSubmit}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            {invoiceId ? 'Edit Invoice' : 'Create Invoice'}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<CancelIcon />}
              onClick={() => onClose ? onClose() : navigate('/dashboard/sales/invoices')}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              startIcon={<SaveIcon />}
              type="submit"
              disabled={loading}
            >
              Save
            </Button>
            <Button
              variant="contained"
              startIcon={<SaveAltIcon />}
              onClick={() => {
                formik.handleSubmit();
                // Additional logic for Save & New
              }}
              disabled={loading}
              sx={{ ml: 1 }}
            >
              Save & New
            </Button>
          </Box>
        </Box>

        {/* Error Display */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={clearError}>
            {error}
          </Alert>
        )}

        {/* Invoice Header Information */}
        <Accordion expanded={expanded === 'panel1'} onChange={handleAccordionChange('panel1')}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Invoice Information</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Card>
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Autocomplete
                      options={customers}
                      getOptionLabel={(option) => option.name}
                      value={customers.find(c => c.id === formik.values.customer) || null}
                      onChange={(_, value) => formik.setFieldValue('customer', value?.id || null)}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Customer *"
                          error={formik.touched.customer && Boolean(formik.errors.customer)}
                          helperText={formik.touched.customer && formik.errors.customer}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <DatePicker
                      label="Invoice Date *"
                      value={dayjs(formik.values.invoice_date)}
                      onChange={(value) => formik.setFieldValue('invoice_date', value?.format('YYYY-MM-DD'))}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          error: formik.touched.invoice_date && Boolean(formik.errors.invoice_date),
                          helperText: formik.touched.invoice_date && formik.errors.invoice_date,
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Autocomplete
                      options={paymentTerms}
                      getOptionLabel={(option) => option.name}
                      value={paymentTerms.find(pt => pt.id === formik.values.payment_terms) || null}
                      onChange={(_, value) => formik.setFieldValue('payment_terms', value?.id || null)}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Payment Terms *"
                          error={formik.touched.payment_terms && Boolean(formik.errors.payment_terms)}
                          helperText={formik.touched.payment_terms && formik.errors.payment_terms}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="PO Number"
                      name="po_number"
                      value={formik.values.po_number}
                      onChange={formik.handleChange}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Autocomplete
                      options={salesReps}
                      getOptionLabel={(option) => option.full_name}
                      value={salesReps.find(sr => sr.id === formik.values.sales_rep) || null}
                      onChange={(_, value) => formik.setFieldValue('sales_rep', value?.id || null)}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Sales Rep"
                        />
                      )}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </AccordionDetails>
        </Accordion>

        {/* Line Items */}
        <Accordion expanded={expanded === 'panel2'} onChange={handleAccordionChange('panel2')} sx={{ mt: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Line Items</Typography>
            {inventoryCheck && !inventoryCheck.all_available && (
              <Chip
                icon={<WarningIcon />}
                label="Inventory Issues"
                color="warning"
                size="small"
                sx={{ ml: 2 }}
              />
            )}
          </AccordionSummary>
          <AccordionDetails>
            <Card>
              <CardContent>
                {/* Line Items Table would go here - simplified for now */}
                <Box sx={{ mb: 2 }}>
                  <Button
                    startIcon={<AddIcon />}
                    onClick={handleAddLineItem}
                    variant="outlined"
                  >
                    Add Line Item
                  </Button>
                  {invoiceId && (
                    <Button
                      startIcon={<InventoryIcon />}
                      onClick={handleInventoryCheck}
                      variant="outlined"
                      disabled={inventoryLoading}
                      sx={{ ml: 2 }}
                    >
                      Check Inventory
                    </Button>
                  )}
                </Box>
                
                {/* Line Items List */}
                {formik.values.line_items.map((item, index) => (
                  <Card key={index} variant="outlined" sx={{ mb: 2 }}>
                    <CardContent>
                      <Grid container spacing={2} alignItems="center">
                        <Grid item xs={12} md={3}>
                          <Autocomplete
                            options={products}
                            getOptionLabel={(option) => `${option.code} - ${option.name}`}
                            value={products.find(p => p.id === item.product) || null}
                            onChange={(_, value) => handleLineItemChange(index, 'product', value?.id || null)}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                label="Product *"
                                size="small"
                              />
                            )}
                          />
                        </Grid>
                        <Grid item xs={12} md={3}>
                          <TextField
                            fullWidth
                            label="Description"
                            value={item.description}
                            onChange={(e) => handleLineItemChange(index, 'description', e.target.value)}
                            size="small"
                          />
                        </Grid>
                        <Grid item xs={12} md={2}>
                          <QuantityInput
                            label="Quantity *"
                            value={item.quantity}
                            onChange={(value) => handleLineItemChange(index, 'quantity', value)}
                            size="small"
                          />
                        </Grid>
                        <Grid item xs={12} md={2}>
                          <FormattedCurrencyInput
                            label="Unit Price"
                            value={item.unit_price || '0.00'}
                            onChange={(value) => handleLineItemChange(index, 'unit_price', value)}
                            currency={companyInfo?.functionalCurrency}
                            size="small"
                          />
                        </Grid>
                        <Grid item xs={12} md={1}>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {formatCurrency(
                              parseFloat(item.quantity || '0') * parseFloat(item.unit_price || '0'),
                              companyInfo?.functionalCurrency
                            )}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} md={1}>
                          <IconButton
                            onClick={() => handleRemoveLineItem(index)}
                            disabled={formik.values.line_items.length === 1}
                            color="error"
                            size="small"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                ))}
              </CardContent>
            </Card>
          </AccordionDetails>
        </Accordion>

        {/* Totals */}
        <Card sx={{ mt: 2 }}>
          <CardContent>
            <Grid container spacing={2} justifyContent="flex-end">
              <Grid item xs={12} md={4}>
                <Box sx={{ textAlign: 'right' }}>
                  <Typography variant="body1">
                    Subtotal: {formatCurrency(totals.subtotal, companyInfo?.functionalCurrency)}
                  </Typography>
                  <Typography variant="body1">
                    Tax: {formatCurrency(totals.totalTax, companyInfo?.functionalCurrency)}
                  </Typography>
                  <Typography variant="body1">
                    Discount: {formatCurrency(totals.discountAmount, companyInfo?.functionalCurrency)}
                  </Typography>
                  <Typography variant="body1">
                    Shipping: {formatCurrency(totals.shippingAmount, companyInfo?.functionalCurrency)}
                  </Typography>
                  <Divider sx={{ my: 1 }} />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Total: {formatCurrency(totals.total, companyInfo?.functionalCurrency)}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Additional Information */}
        <Accordion expanded={expanded === 'panel3'} onChange={handleAccordionChange('panel3')} sx={{ mt: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Additional Information</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Card>
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Memo"
                      name="memo"
                      value={formik.values.memo}
                      onChange={formik.handleChange}
                      multiline
                      rows={3}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Terms & Conditions"
                      name="terms_conditions"
                      value={formik.values.terms_conditions}
                      onChange={formik.handleChange}
                      multiline
                      rows={3}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Footer Text"
                      name="footer_text"
                      value={formik.values.footer_text}
                      onChange={formik.handleChange}
                      multiline
                      rows={2}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </AccordionDetails>
        </Accordion>

        {/* Bottom Action Buttons */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 3 }}>
          <Button
            variant="outlined"
            startIcon={<CancelIcon />}
            onClick={() => onClose ? onClose() : navigate('/dashboard/sales/invoices')}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            type="submit"
            disabled={loading}
          >
            Save
          </Button>
          <Button
            variant="contained"
            startIcon={<SaveAltIcon />}
            onClick={() => {
              formik.handleSubmit();
              // Additional logic for Save & Close
            }}
            disabled={loading}
          >
            Save & Close
          </Button>
        </Box>
      </Box>
    </LocalizationProvider>
  );
};

export default InvoiceForm;
